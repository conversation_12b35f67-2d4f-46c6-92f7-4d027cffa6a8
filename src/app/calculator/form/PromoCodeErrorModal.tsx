import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../themes/hooks';

export type PromoCodeErrorModalProps = {
    onSubmit: () => void;
    onClose: () => void;
    visible: boolean;
    title: string;
};

const PromoCodeErrorModal = ({ onClose, visible, title, onSubmit }: PromoCodeErrorModalProps) => {
    const { t } = useTranslation(['promoCodeDetails']);
    const { Modal } = useThemeComponents();

    return (
        <Modal
            cancelText={t('promoCodeDetails:errorModal.actions.no')}
            okText={t('promoCodeDetails:errorModal.actions.yes')}
            onCancel={onClose}
            onOk={onSubmit}
            open={visible}
            title={title}
            width={384}
            zIndex={2000}
            centered
        >
            {t('promoCodeDetails:errorModal:body')}
        </Modal>
    );
};

export default PromoCodeErrorModal;

export const usePromoCodeErrorModal = () => {
    const [visible, setVisible] = useState(false);
    const [title, setTitle] = useState<string>(null);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
            changeTitle: (value: string) => setTitle(value),
        }),
        [setVisible, setTitle]
    );

    return {
        ...actions,
        render: (onSubmit: () => void) => (
            <PromoCodeErrorModal onClose={actions.close} onSubmit={onSubmit} title={title} visible={visible} />
        ),
    };
};
