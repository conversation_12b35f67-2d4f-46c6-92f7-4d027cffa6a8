export type Theme = {
    verticalSpacing: string;

    loginTheme: LoginTheme;

    dashboardTheme: DashboardTheme;

    customerDetailsTheme: CustomerDetailsTheme;

    footerTheme: FooterTheme;

    layoutType?: string;
};

type LoginTheme = {
    titleColor: string;
    brandWelcomeBackgroundColor: string;
    brandWelcomeTextColor: string;
};

const defaultLoginTheme = {
    titleColor: '#4e4c54',
    brandWelcomeBackgroundColor: '#222034',
    brandWelcomeTextColor: '#fff',
};

type CustomerDetailsTheme = {
    borderColor: string;
    backgroundColor: string;
    inputPrefixColor: string;
    ocrModalTheme: OcrModalTheme;
};

type OcrModalTheme = {
    closeIconColor: string;
};

const defaultCustomerDetailsTheme = {
    borderColor: '#ced4da',
    backgroundColor: '#fff',
    inputPrefixColor: '#cc0033',
    ocrModalTheme: {
        closeIconColor: '#999999',
    },
};

type FooterTheme = {
    copyrightTextColor: string;
    disclaimerTextColor: string;
    selectedLanguageColor: string;
    deselectedLanguageColor: string;
};

const defaultFooterTheme = {
    copyrightTextColor: '#aaa',
    disclaimerTextColor: '#666',
    selectedLanguageColor: '#000',
    deselectedLanguageColor: '#aaa',
};

type DashboardTheme = {
    chartColor: string[];
    statistics: {
        backgroundColor: string;
        color: string;
    };
};

const defaultDashboardTheme = {
    chartColor: ['#67b7dc', '#a367dc'],
    statistics: {
        backgroundColor: '#acb7bf',
        color: '#fff',
    },
};

const theme: Theme = {
    verticalSpacing: '20px',
    loginTheme: defaultLoginTheme,
    dashboardTheme: defaultDashboardTheme,
    customerDetailsTheme: defaultCustomerDetailsTheme,
    footerTheme: defaultFooterTheme,
};

export default theme;
