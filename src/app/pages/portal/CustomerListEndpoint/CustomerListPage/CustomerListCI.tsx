import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import type { ColumnsType } from 'antd/lib/table';
import { isNil, pick, sortBy } from 'lodash/fp';
import type { Key } from 'react';
import { useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, Link } from 'react-router';
import * as permissionKind from '../../../../../shared/permissions';
// eslint-disable-next-line max-len
import type { CustomerListEndpointContextDataFragment } from '../../../../api/fragments/CustomerListEndpointContextData';
import type { CustomerListSpecsFragment } from '../../../../api/fragments/CustomerListSpecs';
import type { EndpointContextDataFragment } from '../../../../api/fragments/EndpointContextData';
import type { LeadInCustomerListFragment } from '../../../../api/fragments/LeadInCustomerList';
import type { GetCustomerListFiltersQueryHookResult } from '../../../../api/queries/getCustomerListFilters';
import { useListCustomersQuery } from '../../../../api/queries/listCustomers';
import { CustomerSortingField, LeadStageOption, SortingOrder } from '../../../../api/types';
import type { ApplicationStage, CustomerFilteringRule, LeadStatus } from '../../../../api/types';
import FilterBox from '../../../../components/FilterBox';
import RangeDateFilterBox, { objectToRangeDateValue } from '../../../../components/RangeDateFilterBox';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import { useRouter } from '../../../../components/contexts/shared';
import useSearchInDropDown from '../../../../components/useSearchInDropDown';
import { useThemeComponents } from '../../../../themes/hooks';
import hasPermissions from '../../../../utilities/hasPermissions';
import renderFilterIcon from '../../../../utilities/renderFilterIcon';
import useFormatDate from '../../../../utilities/useFormatDate';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../../utilities/useSortAndFilterCache';
import type { PaginatedCustomerTable } from '../../../admin/CustomerListPage/CustomerList';
import { useListReducer, defaultInitialState } from '../../../admin/CustomerListPage/useListReducer';
import useSortOrder, { getSortField } from '../../../admin/CustomerListPage/useSortOrder';
import { renderLeadStatusTag } from '../../../shared/ApplicationList/helpers';

export type CustomerListCIProps = {
    endpoint: CustomerListEndpointContextDataFragment;
    setModuleListByApplication?: React.Dispatch<React.SetStateAction<string[]>>;
};

type CustomerDataSource = CustomerListSpecsFragment & { key: Key };

export const getApplicationReferenceLink = (
    applicationStage: ApplicationStage,
    applicationStageIdentifier: string,
    applicationId: string,
    isClickable: boolean,
    applicationModuleId: string,
    routerEndpoints: EndpointContextDataFragment[]
) => {
    const endpoint = routerEndpoints.find(
        endpoint =>
            endpoint.__typename === 'ApplicationListEndpoint' &&
            endpoint.applicationStage === applicationStage &&
            endpoint.applicationModuleIds.includes(applicationModuleId)
    );
    const getLinkComponent = (link: string) =>
        isClickable ? (
            <Link onClick={e => e.stopPropagation()} to={link}>
                {applicationStageIdentifier}
            </Link>
        ) : (
            applicationStageIdentifier
        );

    if (!isNil(endpoint)) {
        return getLinkComponent(`${endpoint.pathname}/${applicationId}`);
    }

    return applicationStageIdentifier;
};

export const getLeadReferenceLink = (
    identifier: string,
    leadId: string,
    isLead: boolean,
    isClickable: boolean,
    routerEndpoints: EndpointContextDataFragment[]
) => {
    const endpoint = routerEndpoints.find(endpoint => {
        if (endpoint.__typename !== 'LeadListEndpoint') {
            return false;
        }

        const stages = [LeadStageOption.LeadAndContact, isLead ? LeadStageOption.Lead : LeadStageOption.Contact];

        return stages.includes(endpoint.leadStage);
    });

    if (!isNil(endpoint) && isClickable) {
        return (
            <Link onClick={e => e.stopPropagation()} to={`${endpoint.pathname}/${leadId}`}>
                {identifier}
            </Link>
        );
    }

    return identifier;
};

const CustomerListCI = ({ endpoint, setModuleListByApplication }: CustomerListCIProps) => {
    const { applicationModuleIds } = endpoint;
    const { t } = useTranslation(['applicationList', 'customerList']);
    const { PaginatedTableWithContext } = useThemeComponents();
    const navigate = useNavigate();

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Customer);

    // get state from a reducer
    const [state, dispatch] = useListReducer<CustomerSortingField, CustomerFilteringRule>({
        sort: currentCache?.sort || {
            field: CustomerSortingField.CreationDate,
            order: SortingOrder.Desc,
        },
        page: currentCache?.page || defaultInitialState.page,
        pageSize: currentCache?.pageSize || defaultInitialState.pageSize,
        filter: currentCache?.filter || {
            keyword: '',
        },
    });

    const formatDate = useFormatDate();

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { dealerIds } = useMultipleDealerIds();

    const { data, loading } = useListCustomersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: {
                ...filter,
                applicationModuleIds: filter?.applicationModuleIds ?? applicationModuleIds,
                applicationDealerIds: filter?.applicationDealerIds ?? dealerIds,
            },
        },
    });

    const customerFilterList: GetCustomerListFiltersQueryHookResult['data'] = null;

    const filterListMapper = list =>
        list.map(field => ({
            text: field.displayName,
            value: field.id,
        }));

    const [statusList, companyList, dealerList, moduleList] = useMemo(() => {
        const filterList = customerFilterList?.getCustomerListFilters;
        if (!filterList) {
            return [[], [], [], []];
        }
        const statusList = sortBy(
            'text',
            filterList.applicationStatus.map(status => ({
                text: t(`applicationList:status.${status}`),
                value: status,
            }))
        );
        const companyList = sortBy('text', filterListMapper(filterList.companies));
        const dealerList = sortBy('text', filterListMapper(filterList.dealers));
        const moduleList = sortBy('text', filterListMapper(filterList.modules));

        return [statusList, companyList, dealerList, moduleList];
    }, [customerFilterList, t]);

    useEffect(() => {
        if (setModuleListByApplication) {
            const moduleListIds = moduleList.map(module => module.value);
            setModuleListByApplication(moduleListIds);
        }
    }, [moduleList, setModuleListByApplication]);

    const onRow = useCallback<PaginatedCustomerTable['onRow']>(
        record => ({
            onClick: () => {
                setCache(state);
                navigate(record.versioning.suiteId);
            },
        }),
        [navigate, setCache, state]
    );

    const showRangeFilterDropDown = useCallback(
        (props: FilterDropdownProps) =>
            RangeDateFilterBox({
                filterDropDownProps: props,
                picker: 'date',
            }),
        []
    );

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });
                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filteringRule: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            applicationIdentifier: !filters.applicationIdentifier
                                ? null
                                : filters.applicationIdentifier[0],
                            assigneeName: !filters.assigneeName ? null : filters.assigneeName[0],
                            fullName: !filters.fullName ? null : filters.fullName[0],
                            applicationModuleIds: !filters.applicationModuleIds ? null : filters.applicationModuleIds,
                            applicationStatuses: !filters.status ? null : filters.status,
                            applicationDealerIds: !filters.dealer ? null : filters.dealer,
                            lastActivity: !filters.lastActivity
                                ? null
                                : {
                                      start: new Date(filters.lastActivity[0] as string),
                                      end: new Date(filters.lastActivity[1] as string),
                                  },
                            creationDate: !filters.creationDate
                                ? null
                                : {
                                      start: new Date(filters.creationDate[0] as string),
                                      end: new Date(filters.creationDate[1] as string),
                                  },
                        },
                    });
                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const router = useRouter();

    // prepare items as a data source
    const dataSource = useMemo(
        () =>
            (data?.list?.items || []).map(item => ({
                ...item,
                key: item.id,
            })),
        [data]
    );
    const total = data?.list?.count || 0;
    const searchBox = useSearchInDropDown();

    const {
        companySort,
        customerNameSort,
        dealerSort,
        appModuleSort,
        assigneeSort,
        leadIdSort,
        leadStatusSort,
        createDateSort,
        lastActivitySort,
    } = useSortOrder(sort);

    const getFormattedDate = useCallback(
        (
            preferred: { date: string | Date | undefined; moduleId: string; timeZone?: string },
            fallback: { date: string | Date | undefined; moduleId: string; timeZone?: string }
        ) => {
            if (isNil(preferred.date) && isNil(fallback.date)) {
                return '';
            }

            const valid = isNil(preferred.date) ? fallback : preferred;

            return formatDate({
                date: valid.date,
                timeZone: valid.timeZone,
                withOffset: true,
            });
        },
        [formatDate]
    );

    const columns: ColumnsType<CustomerDataSource> = useMemo(() => {
        const cols = [
            {
                key: 'company',
                dataIndex: ['latestLead', 'module', 'company', 'displayName'],
                title: t('customerList:columns.company'),
            },
            {
                key: 'fullName',
                dataIndex: 'latestFullName',
                onFilterDropdownVisibleChange: searchBox.autoFocus,
                title: t('customerList:columns.fullName'),
            },
            {
                key: 'creationDate',
                dataIndex: ['versioning', 'createdAt'],
                filterDropdown: props => showRangeFilterDropDown(props),
                filterIcon: renderFilterIcon,
                render: (value: string | Date, record: CustomerListSpecsFragment) =>
                    !isNil(value)
                        ? formatDate({
                              date: value,
                              timeZone: record?.latestLead?.module?.company?.timeZone,
                              withOffset: true,
                          })
                        : '',
                title: t('customerList:columns.creationDate'),
                sorter: true,
                sortOrder: createDateSort,
                filteredValue: objectToRangeDateValue(filter.creationDate),
            },
            {
                key: 'dealer',
                dataIndex: ['latestLead', 'dealer', 'displayName'],
                title: t('customerList:columns.dealer'),
            },
            {
                key: 'assigneeName',
                dataIndex: ['latestLead', 'assignee', 'displayName'],
                title: t('customerList:columns.assignee'),
            },
            {
                key: 'leadModuleIds',
                dataIndex: ['latestLead', 'module', 'displayName'],
                title: t('customerList:columns.module'),
            },
            {
                key: 'leadIdentifier',
                dataIndex: ['latestLead'],
                render: (latestLead: LeadInCustomerListFragment) =>
                    getLeadReferenceLink(
                        latestLead?.identifier,
                        latestLead?.versioning?.suiteId,
                        latestLead.isLead
                            ? hasPermissions(latestLead.permissions, [permissionKind.viewLeads])
                            : hasPermissions(latestLead.permissions, [permissionKind.viewContact]),
                        latestLead?.isLead,
                        router?.endpoints
                    ),
                title: t('customerList:columns.latestContactOrLead'),
            },
            {
                key: 'status',
                dataIndex: ['latestLead', 'status'],
                render: (value: LeadStatus) => renderLeadStatusTag(value, t),
                title: t('customerList:columns.latestStatus'),
            },
            {
                key: 'lastActivity',
                dataIndex: ['latestLead', 'versioning', 'updatedAt'],
                render: (value: string | Date, record: CustomerListSpecsFragment) =>
                    getFormattedDate(
                        {
                            date: value,
                            moduleId: record.latestLead?.moduleId,
                            timeZone: record?.latestLead?.module?.company?.timeZone,
                        },
                        {
                            date: record.latestLead?.versioning?.updatedAt,
                            moduleId: record.latestLead?.moduleId,
                            timeZone: record?.latestLead?.module?.company?.timeZone,
                        }
                    ),
                title: t('customerList:columns.lastActivity'),
            },
        ];

        return company ? cols.filter(col => col.key !== 'company') : cols;
    }, [
        t,
        searchBox.autoFocus,
        createDateSort,
        filter.creationDate,
        company,
        showRangeFilterDropDown,
        formatDate,
        router?.endpoints,
        getFormattedDate,
    ]);

    return (
        <PaginatedTableWithContext
            columns={columns}
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={onRow}
            rowKey="id"
            state={state}
            tableName={t('customerList:title')}
            total={total}
        />
    );
};

export default CustomerListCI;
