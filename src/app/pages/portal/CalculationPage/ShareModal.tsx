import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import Form from '../../../components/fields/Form';
import OutlinedInputField from '../../../components/fields/OutlinedInputField';
import useValidator from '../../../utilities/useValidator';
import validators from '../../../utilities/validators';

interface ShareModalProps {
    openShare: boolean;
    setOpenShare: (boolean) => void;
}

const initialValues = {
    username: '',
    email: '',
};

const formValidator = validators.compose(validators.requiredString('username'), validators.requiredString('email'));

const Title = styled.div`
    text-align: center;
`;

const noop = () => undefined;

const ShareModal = (props: ShareModalProps) => {
    const { openShare, setOpenShare } = props;
    const { t } = useTranslation(['calculation']);
    const validate = useValidator(formValidator);

    return (
        <Modal
            footer={null}
            okButtonProps={{ style: { width: '100%' } }}
            onCancel={() => setOpenShare(false)}
            onOk={() => setOpenShare(false)}
            title={<Title>{t('calculation:actions.share')}</Title>}
            visible={openShare}
            centered
        >
            <Formik initialValues={initialValues} onSubmit={noop} validate={validate}>
                {() => (
                    <Form>
                        <Row gutter={[16, 16]}>
                            <Col span={24}>
                                <OutlinedInputField
                                    {...t('calculation:fields.name', { returnObjects: true })}
                                    bordered={false}
                                    name="username"
                                    allowClear
                                    required
                                />
                            </Col>
                            <Col span={24}>
                                <OutlinedInputField
                                    {...t('calculation:fields.email', { returnObjects: true })}
                                    bordered={false}
                                    name="email"
                                    allowClear
                                    required
                                />
                            </Col>
                            <Col span={24}>
                                <Button size="large" type="primary" block>
                                    {t('calculation:actions.send')}
                                </Button>
                            </Col>
                        </Row>
                    </Form>
                )}
            </Formik>
        </Modal>
    );
};

export default ShareModal;
