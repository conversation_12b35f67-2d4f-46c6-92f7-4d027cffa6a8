import type { InputRef } from 'antd';
import { Table } from 'antd';
import { pick } from 'lodash/fp';
import { useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { BankListDataFragment } from '../../../api/fragments/BankListData';
import { useListBanksQuery } from '../../../api/queries/listBanks';
import { BankSortingField } from '../../../api/types';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import SearchBox from '../../../components/SearchBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import { checkHasFilter } from '../../../utilities/common';
import makeGetSortingRule from '../../../utilities/makeGetSortingRule';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import BankEmptyListResult from './BankEmptyListResult';
import useListReducer from './useListReducer';

const getSortField = makeGetSortingRule((field): BankSortingField => {
    switch (field) {
        case 'displayName':
            return BankSortingField.DisplayName;

        case 'module.company.displayName':
            return BankSortingField.CompanyDisplayName;

        case 'order':
            return BankSortingField.Order;

        default:
            throw new Error('Unknown Sorting Field!');
    }
});

const BankList = () => {
    const { t } = useTranslation('bankList');

    const inputRef = useRef<InputRef>(null);

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Bank);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListBanksQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { ...filter, companyId: company?.id },
        },
    });

    const navigate = useNavigate();

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            displayName: !filters.displayName ? null : filters.displayName[0],
                            companyDisplayName: !filters.companyDisplayName ? null : filters.companyDisplayName[0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // prepare items as a data source
    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    const showFilterDropDown = useCallback(
        (props, dataIndex) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const onFilterDropdownVisibleChange = useCallback(
        visible => {
            if (visible) {
                setTimeout(() => inputRef.current.select(), 100);
            }
        },
        [inputRef]
    );

    const hasFilter = useMemo(() => checkHasFilter(filter), [filter]);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (dataSource.length === 0 && !hasFilter) {
            return <BankEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: BankListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/banks/${row.id}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            state={state}
            tableName={t('bankList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="companyDisplayName"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, t('bankList:columns.companyDisplayName'))}
                    filterIcon={renderSearchIcon}
                    filteredValue={filter.companyDisplayName ? [filter.companyDisplayName] : undefined}
                    onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                    sortOrder={sort.field === BankSortingField.CompanyDisplayName ? sort.orderValue : undefined}
                    title={t('bankList:columns.companyDisplayName')}
                    sorter
                />
            )}
            <Table.Column
                key="displayName"
                dataIndex="displayName"
                filterDropdown={props => showFilterDropDown(props, t('bankList:columns.displayName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.displayName ? [filter.displayName] : undefined}
                onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                sortOrder={sort.field === BankSortingField.DisplayName ? sort.orderValue : undefined}
                title={t('bankList:columns.displayName')}
                sorter
            />
            <Table.Column
                key="order"
                dataIndex="order"
                sortOrder={sort.field === BankSortingField.Order ? sort.orderValue : undefined}
                title={t('bankList:columns.order')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                title={t('bankList:columns.isActive')}
                width={120}
            />
        </PaginatedTableWithContext>
    );
};

export default BankList;
