import type { InputRef } from 'antd';
import { Table } from 'antd';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { pick } from 'lodash/fp';
import { useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { ModelConfiguratorListDataFragment } from '../../../api/fragments/ModelConfiguratorListData';
import { useListModelConfiguratorsQuery } from '../../../api/queries/listModelConfigurators';
import { CompanyFilterListCollection } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import SearchBox from '../../../components/SearchBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import { checkHasFilter } from '../../../utilities/common';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useGetCompanyFilter from '../../../utilities/useGetCompanyFilter';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import ModelConfiguratorEmptyListResult from './ModelConfiguratorEmptyListResult';
import useListReducer from './useListReducer';
import useSortOrder, { getSortField } from './useSortOrder';

const ConfiguratorList = () => {
    const { t } = useTranslation('configuratorList');

    const inputRef = useRef<InputRef>(null);

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Configurator);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListModelConfiguratorsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { companyIds: company ? [company.id] : undefined, ...filter },
        },
    });

    const companyFilterList = useGetCompanyFilter({ collection: CompanyFilterListCollection.Configurators });

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            configuratorModuleDisplayName: !filters['module.displayName']
                                ? null
                                : filters['module.displayName'][0],
                            vehicleModelDisplayName: !filters.displayName ? null : filters.displayName[0],
                            vehicleModuleDisplayName: !filters['vehicleModule.displayName']
                                ? null
                                : filters['vehicleModule.displayName'][0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    const navigate = useNavigate();

    // prepare items as a data source
    const dataSource = useMemo(() => (data?.lists?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.lists?.count || 0;

    const showSearchDropDown = useCallback(
        (props, dataIndex) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const onFilterDropdownVisibleChange = useCallback(
        visible => {
            if (visible) {
                setTimeout(() => inputRef.current.select(), 100);
            }
        },
        [inputRef]
    );

    const {
        companySort,
        displayNameSort,
        variantConfiguratorCountSort,
        configuratorModuleDisplayNameSort,
        vehicleModuleDisplayNameSort,
    } = useSortOrder(sort);

    const hasFilter = useMemo(() => checkHasFilter(filter), [filter]);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (dataSource.length === 0 && !hasFilter) {
            return <ModelConfiguratorEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: ModelConfiguratorListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/configurators/${row.id}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            state={state}
            tableName={t('configuratorList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="company"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, companyFilterList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.companyIds}
                    sortOrder={companySort}
                    title={t('configuratorList:columns.company')}
                    sorter
                />
            )}
            <Table.Column
                dataIndex={['module', 'displayName']}
                filterDropdown={props => showSearchDropDown(props, t('configuratorList:columns.moduleDisplayName'))}
                filterIcon={renderSearchIcon}
                filteredValue={
                    filter.configuratorModuleDisplayName ? [filter.configuratorModuleDisplayName] : undefined
                }
                onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                sortOrder={configuratorModuleDisplayNameSort}
                title={t('configuratorList:columns.moduleDisplayName')}
                sorter
            />
            <Table.Column
                dataIndex={['vehicleModule', 'displayName']}
                filterDropdown={props =>
                    showSearchDropDown(props, t('configuratorList:columns.vehicleModuleDisplayName'))
                }
                filterIcon={renderSearchIcon}
                filteredValue={filter.vehicleModuleDisplayName ? [filter.vehicleModuleDisplayName] : undefined}
                onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                sortOrder={vehicleModuleDisplayNameSort}
                title={t('configuratorList:columns.vehicleModuleDisplayName')}
                sorter
            />
            <Table.Column
                dataIndex="displayName"
                filterDropdown={props => showSearchDropDown(props, t('configuratorList:columns.displayName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.vehicleModelDisplayName ? [filter.vehicleModelDisplayName] : undefined}
                onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                sortOrder={displayNameSort}
                title={t('configuratorList:columns.displayName')}
                sorter
            />
            <Table.Column
                dataIndex="variantConfiguratorIds"
                render={variantConfiguratorIds => variantConfiguratorIds.length}
                sortOrder={variantConfiguratorCountSort}
                title={t('configuratorList:columns.variantConfigurators')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                title={t('configuratorList:columns.isActive')}
                width={120}
            />
        </PaginatedTableWithContext>
    );
};

export default ConfiguratorList;
