import { useApolloClient } from '@apollo/client';
import { Form, message } from 'antd';
import { Formik } from 'formik';
import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { CreateRoleMutation, CreateRoleMutationVariables } from '../../../api/mutations/createRole';
import { CreateRoleDocument } from '../../../api/mutations/createRole';
import { useListCompaniesForSelectionQuery } from '../../../api/queries/listCompaniesForSelection';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InputField from '../../../components/fields/InputField';
import SelectField from '../../../components/fields/SelectField';
import SwitchField from '../../../components/fields/SwitchField';
import { useThemeComponents } from '../../../themes/hooks';
import useHandleError from '../../../utilities/useHandleError';
import useSystemSwitchData from '../../../utilities/useSystemSwitchData';
import useValidator from '../../../utilities/useValidator';
import validators from '../../../utilities/validators';

export type CreateRoleModalProps = { visible: boolean; onClose: () => void };

type FormValues = { displayName: string; companyId: string | null; isActive: boolean };

const formValidator = validators.compose(
    validators.requiredString('displayName'),
    validators.requiredString('companyId')
);

const CreationForm = () => {
    const { t } = useTranslation(['roleList']);

    // get company context
    const company = useCompany(true);

    const { yesNoSwitch } = useSystemSwitchData();

    const { data } = useListCompaniesForSelectionQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            filter: {
                companyId: company?.id,
            },
        },
    });

    const companies = data?.list?.items || [];

    return (
        <>
            <InputField
                {...t('roleList:creationModal.fields.displayName', { returnObjects: true })}
                name="displayName"
                required
            />
            <SelectField
                {...t('roleList:creationModal.fields.company', { returnObjects: true })}
                name="companyId"
                options={companies.map(company => ({ value: company.id, label: company.displayName }))}
                required
                showSearch
            />
            <SwitchField
                {...t('roleList:creationModal.fields.isActive', { returnObjects: true })}
                name="isActive"
                {...yesNoSwitch}
            />
        </>
    );
};

const CreateRoleModal = ({ visible, onClose }: CreateRoleModalProps) => {
    const navigate = useNavigate();
    const { t } = useTranslation(['roleList']);
    const apolloClient = useApolloClient();
    const { Modal } = useThemeComponents();

    const validate = useValidator(formValidator);

    const onSubmit = useHandleError<FormValues>(
        async values => {
            // show loading message
            message.loading({
                content: t('roleList:creationModal.messages.submitting'),
                duration: 0,
                key: 'primary',
            });

            if (!values.companyId) {
                throw new Error('Company ID is missing');
            }

            // call mutation to create the new pack
            const { data } = await apolloClient
                .mutate<CreateRoleMutation, CreateRoleMutationVariables>({
                    mutation: CreateRoleDocument,
                    variables: values,
                })
                .finally(() => {
                    // always clean up
                    message.destroy('primary');
                });

            // show successful message
            message.success({
                content: t('roleList:creationModal.messages.success'),
                key: 'primary',
            });

            // then browse to the new pack
            navigate(`/admin/accesses/roles/${data.role.id}`);
        },
        [apolloClient, t, navigate]
    );

    return (
        <Modal
            cancelText={t('roleList:creationModal.cancelText')}
            okButtonProps={{ htmlType: 'submit', form: 'roleCreation' }}
            okText={t('roleList:creationModal.okText')}
            onCancel={onClose}
            title={t('roleList:creationModal.title')}
            visible={visible}
            destroyOnClose
        >
            <Formik<FormValues>
                initialValues={{ displayName: '', companyId: null, isActive: false }}
                onSubmit={onSubmit}
                validate={validate}
            >
                {({ handleSubmit }) => (
                    <Form id="roleCreation" layout="vertical" name="roleCreation" onSubmitCapture={handleSubmit}>
                        <CreationForm />
                    </Form>
                )}
            </Formik>
        </Modal>
    );
};

export default CreateRoleModal;

export const useCreationModal = () => {
    const [visible, setVisible] = useState(false);
    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    const render = () => <CreateRoleModal onClose={actions.close} visible={visible} />;

    return { visible, ...actions, render };
};
