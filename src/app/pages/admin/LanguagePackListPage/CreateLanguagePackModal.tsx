import { useApolloClient } from '@apollo/client';
import { Form, message } from 'antd';
import { Formik } from 'formik';
import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type {
    CreateLanguagePackMutation,
    CreateLanguagePackMutationVariables,
} from '../../../api/mutations/createLanguagePack';
import { CreateLanguagePackDocument } from '../../../api/mutations/createLanguagePack';
import InputField from '../../../components/fields/InputField';
import { useThemeComponents } from '../../../themes/hooks';
import useHandleError from '../../../utilities/useHandleError';
import useLanguagePackValidator from './useLanguagePackValidator';

export type CreateLanguagePackModalProps = { visible: boolean; onClose: () => void };

type FormValues = { referenceName: string; displayName: string };

const initialValues: FormValues = {
    referenceName: '',
    displayName: '',
};

const CreateLanguagePackModal = ({ visible, onClose }: CreateLanguagePackModalProps) => {
    const navigate = useNavigate();
    const { t } = useTranslation(['languagePackList', 'languagePackDetails']);
    const apolloClient = useApolloClient();
    const validate = useLanguagePackValidator();
    const { Modal } = useThemeComponents();

    const onSubmit = useHandleError<FormValues>(
        async values => {
            // show loading message
            message.loading({
                content: t('languagePackList:messages.creationSubmitting'),
                duration: 0,
                key: 'primary',
            });

            // call mutation to create the new pack
            const { data } = await apolloClient
                .mutate<CreateLanguagePackMutation, CreateLanguagePackMutationVariables>({
                    mutation: CreateLanguagePackDocument,
                    variables: { referenceName: values.referenceName, displayName: values.displayName },
                })
                .finally(() => {
                    // always clean up
                    message.destroy('primary');
                });

            // show successful message
            message.success({
                content: t('languagePackList:messages.creationSuccessful'),
                key: 'primary',
            });

            // then browse to the new pack
            navigate(`/admin/system/languages/${data.language.id}`);
        },
        [apolloClient, t, navigate]
    );

    return (
        <Formik<FormValues> initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
            {({ handleSubmit, isValid }) => (
                <Modal
                    cancelText={t('languagePackList:creationModal.cancelText')}
                    okButtonProps={{ htmlType: 'submit', form: 'languageCreation', disabled: !isValid }}
                    okText={t('languagePackList:creationModal.okText')}
                    onCancel={onClose}
                    title={t('languagePackList:creationModal.title')}
                    visible={visible}
                    destroyOnClose
                >
                    <Form id="languageCreation" name="languageCreation" onSubmitCapture={handleSubmit}>
                        <InputField
                            {...t('languagePackDetails:fields.referenceName', { returnObjects: true })}
                            name="referenceName"
                            required
                        />
                        <InputField
                            {...t('languagePackDetails:fields.displayName', { returnObjects: true })}
                            name="displayName"
                            required
                        />
                    </Form>
                </Modal>
            )}
        </Formik>
    );
};

export default CreateLanguagePackModal;

export const useCreationModal = () => {
    const [visible, setVisible] = useState(false);
    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    const render = () => <CreateLanguagePackModal onClose={actions.close} visible={visible} />;

    return { visible, ...actions, render };
};
