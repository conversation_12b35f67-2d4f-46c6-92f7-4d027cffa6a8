import type { InputRef } from 'antd';
import { Table } from 'antd';
import { pick } from 'lodash/fp';
import { useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { InsurerListDataFragment } from '../../../api/fragments/InsurerListData';
import { useListInsurersQuery } from '../../../api/queries/listInsurers';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import SearchBox from '../../../components/SearchBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import { checkHasFilter } from '../../../utilities/common';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import InsurerEmptyListResult from './InsurerEmptyListResult';
import useListReducer from './useListReducer';
import useSortOrder, { getSortField } from './useSortOrder';

const InsurerList = () => {
    const { t } = useTranslation('insurerList');

    const inputRef = useRef<InputRef>(null);

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Insurer);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListInsurersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { ...filter, companyId: company?.id },
        },
    });

    const navigate = useNavigate();

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            displayName: !filters.displayName ? null : filters.displayName[0],
                            companyDisplayName: !filters.companyDisplayName ? null : filters.companyDisplayName[0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // prepare items as a data source
    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    const showFilterDropDown = useCallback(
        (props, dataIndex) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const onFilterDropdownVisibleChange = useCallback(
        visible => {
            if (visible) {
                setTimeout(() => inputRef.current.select(), 100);
            }
        },
        [inputRef]
    );

    const { companySort, displayNameSort, orderSort } = useSortOrder(sort);

    const hasFilter = useMemo(() => checkHasFilter(filter), [filter]);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (dataSource.length === 0 && !hasFilter) {
            return <InsurerEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: InsurerListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/insurers/${row.id}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            state={state}
            tableName={t('insurerList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="companyDisplayName"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, t('insurerList:columns.companyDisplayName'))}
                    filterIcon={renderSearchIcon}
                    filteredValue={filter.companyDisplayName ? [filter.companyDisplayName] : undefined}
                    onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                    sortOrder={companySort}
                    title={t('insurerList:columns.companyDisplayName')}
                    sorter
                />
            )}
            <Table.Column
                key="displayName"
                dataIndex="displayName"
                filterDropdown={props => showFilterDropDown(props, t('insurerList:columns.displayName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.displayName ? [filter.displayName] : undefined}
                onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                sortOrder={displayNameSort}
                title={t('insurerList:columns.displayName')}
                sorter
            />
            <Table.Column
                key="order"
                dataIndex="order"
                sortOrder={orderSort}
                title={t('insurerList:columns.order')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                title={t('insurerList:columns.isActive')}
                width={120}
            />
        </PaginatedTableWithContext>
    );
};

export default InsurerList;
