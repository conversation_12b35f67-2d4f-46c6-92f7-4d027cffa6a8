import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Space, Table, Typography, message } from 'antd';
import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { allowedExtensions } from '../../../../../server/utils/extensions';
import type { ColorBlockSpecsFragment } from '../../../../api/fragments/ColorBlockSpecs';
import ArrayField from '../../../../components/fields/ArrayField';
import ColorPickerField from '../../../../components/fields/ColorPickerField';
import CompanyCurrencyField from '../../../../components/fields/CompanyCurrencyField';
import FormItem from '../../../../components/fields/FormItem';
import SingleUploadField from '../../../../components/fields/SingleUploadField';
import FormFields from '../../../../themes/admin/Fields/FormFields';
import { useThemeComponents } from '../../../../themes/hooks';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { useVariantConfigurator } from '../VariantConfiguratorContext';
import useCheckVariantConfiguratorAvailableStock from '../useCheckVariantConfiguratorAvailableStock';
import type { TrimBlockFormValues, TrimSettingFormValues } from './utils';
import { getDefaultTrimSetting } from './utils';

const getRowFieldName = (parentName: string, index: number, childName: string) => `${parentName}[${index}]${childName}`;

type TrimBlockSettingFieldProps = {
    name: string;
    colorBlock: ColorBlockSpecsFragment;
    disabled?: boolean;
};

const TrimBlockSettingField = ({ name, colorBlock, disabled = false }: TrimBlockSettingFieldProps) => {
    const { t } = useTranslation(['variantConfiguratorDetails']);
    const { ResponsiveStyledTable } = useThemeComponents();

    const { variantConfigurator } = useVariantConfigurator();
    const checkAvailableStock = useCheckVariantConfiguratorAvailableStock();
    const removeSetting = async (
        settingId: string,
        name: string,
        rowIndex: number,
        remove: (rowIndex: number) => TrimSettingFormValues
    ) => {
        const settingDeletable = await checkAvailableStock(settingId, variantConfigurator.id);
        if (isNil(settingDeletable.data.inventory)) {
            remove(rowIndex);
        } else {
            message.warn(t('variantConfiguratorDetails:messages.reservedStock', { name }), 3);
        }
    };

    const translate = useTranslatedString();

    return (
        <ArrayField<TrimSettingFormValues, TrimBlockFormValues>
            name={name}
            render={({ push, remove }, trimSettings) => (
                <Space direction="vertical" style={{ width: '100%' }}>
                    <ResponsiveStyledTable
                        dataSource={trimSettings}
                        pagination={false}
                        rowKey={(record, index) => `${index.toString()}`}
                        outLineBordered
                    >
                        <Table.Column
                            dataIndex="name"
                            render={(_, __, rowIndex: number) => (
                                <FormFields.TranslatedInputField
                                    disabled={disabled}
                                    name={getRowFieldName(name, rowIndex, 'name')}
                                    {...t('variantConfiguratorDetails:fields.trims.settings.name', {
                                        returnObjects: true,
                                    })}
                                />
                            )}
                            title={
                                <FormItem
                                    label={t('variantConfiguratorDetails:fields.trims.settings.name.label')}
                                    required
                                />
                            }
                            width={400}
                        />
                        <Table.Column
                            dataIndex="sectionImages"
                            render={(images: TrimSettingFormValues['sectionImages'], __, rowIndex: number) =>
                                images?.map((sectionImage, index) => (
                                    <div key={`${sectionImage?.colorId}_${rowIndex.toString()}_${index.toString()}`}>
                                        <Typography.Text
                                            ellipsis={{ tooltip: translate(sectionImage?.colorName) }}
                                            style={{ width: 94 }}
                                        >
                                            {translate(sectionImage?.colorName)}
                                        </Typography.Text>
                                        <SingleUploadField
                                            disabled={disabled}
                                            extensions={allowedExtensions.image}
                                            listType="picture-card"
                                            name={getRowFieldName(
                                                name,
                                                rowIndex,
                                                `sectionImages[${index}].variantTrimImage`
                                            )}
                                            size="small"
                                            style={{ margin: 0 }}
                                        />
                                    </div>
                                ))
                            }
                            title={
                                <FormItem
                                    label={t('variantConfiguratorDetails:fields.trims.settings.sectionImage.label')}
                                    required={colorBlock?.colorSettings?.length > 0}
                                />
                            }
                            width={140}
                        />
                        <Table.Column
                            dataIndex="code"
                            render={(_, __, rowIndex: number) => (
                                <FormFields.InputField
                                    disabled={disabled}
                                    name={getRowFieldName(name, rowIndex, 'code')}
                                />
                            )}
                            title={
                                <FormItem label={t('variantConfiguratorDetails:fields.trims.settings.code.label')} />
                            }
                            width={150}
                        />
                        <Table.Column
                            dataIndex="image"
                            render={(_, __, rowIndex: number) => (
                                <SingleUploadField
                                    disabled={disabled}
                                    extensions={allowedExtensions.image}
                                    listType="picture-card"
                                    name={getRowFieldName(name, rowIndex, 'image')}
                                    size="small"
                                />
                            )}
                            title={
                                <FormItem label={t('variantConfiguratorDetails:fields.trims.settings.image.label')} />
                            }
                        />
                        <Table.Column
                            dataIndex="hex"
                            render={(_, __, rowIndex: number) => (
                                <ColorPickerField disabled={disabled} name={getRowFieldName(name, rowIndex, 'hex')} />
                            )}
                            title={
                                <FormItem
                                    label={t('variantConfiguratorDetails:fields.trims.settings.hex.label')}
                                    required
                                />
                            }
                            width={200}
                        />
                        <Table.Column
                            dataIndex="price"
                            render={(_, __, rowIndex: number) => (
                                <CompanyCurrencyField
                                    company={variantConfigurator.company}
                                    disabled={disabled}
                                    name={getRowFieldName(name, rowIndex, 'price')}
                                />
                            )}
                            title={
                                <FormItem label={t('variantConfiguratorDetails:fields.trims.settings.price.label')} />
                            }
                            width={200}
                        />
                        {trimSettings?.length > 1 && (
                            <Table.Column
                                dataIndex="id"
                                render={(_, __, rowIndex: number) =>
                                    !disabled && (
                                        <Button
                                            icon={<DeleteOutlined />}
                                            onClick={() =>
                                                removeSetting(
                                                    trimSettings[rowIndex].id,
                                                    trimSettings[rowIndex].name.defaultValue,
                                                    rowIndex,
                                                    remove
                                                )
                                            }
                                            size="small"
                                            style={{ marginBottom: 16 }}
                                            type="link"
                                            danger
                                        />
                                    )
                                }
                                width={60}
                            />
                        )}
                    </ResponsiveStyledTable>
                    {!disabled && (
                        <Button
                            icon={<PlusOutlined />}
                            onClick={() => push(getDefaultTrimSetting(colorBlock))}
                            type="primary"
                        >
                            {t('variantConfiguratorDetails:actions.trims.addSetting')}
                        </Button>
                    )}
                </Space>
            )}
        />
    );
};

export default TrimBlockSettingField;
