import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, message, Row, Space } from 'antd';
import { useField, useFormikContext } from 'formik';
import { isNil } from 'lodash';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { allowedExtensions } from '../../../../../server/utils/extensions';
import { PackageKind } from '../../../../api/types';
import ArrayField from '../../../../components/fields/ArrayField';
import ImageUploadField from '../../../../components/fields/ImageUploadField';
import TranslatedInputFieldArray from '../../../../components/fields/TranslatedInputFieldArray';
import FormFields from '../../../../themes/admin/Fields/FormFields';
import { useThemeComponents } from '../../../../themes/hooks';
import useSystemOptions from '../../../../utilities/useSystemOptions';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import { useVariantConfigurator } from '../VariantConfiguratorContext';
import useCheckVariantConfiguratorAvailableStock from '../useCheckVariantConfiguratorAvailableStock';
import PackageAdditionalDetailFieldGroup from './PackageAdditionalDetailFieldGroup';
import PackageDescriptionTypeFieldGroup from './PackageDescriptionTypeFieldGroup';
import PackagePriceTypeFieldGroup from './PackagePriceTypeFieldGroup';
import type { PackageBlockFormValues, PackageSettingValues } from './utils';
import { getDefaultPackageSetting } from './utils';

type PackageSettingItemProps = {
    name: string;
    index: number;
    remove: (index: number) => void;
    packageSetting: PackageSettingValues;
    total?: number;
    disabled?: boolean;
};

const PackageSettingItem = ({
    name,
    index,
    total,
    remove,
    packageSetting,
    disabled = false,
}: PackageSettingItemProps) => {
    const { t } = useTranslation(['variantConfiguratorDetails']);
    const { Card } = useThemeComponents();

    const { values, setValues } = useFormikContext<PackageBlockFormValues>();

    const [fieldType] = useField<PackageKind>(`${name}[${index}]packageType.type`);

    const { configurator } = useSystemOptions();

    const { yesNoSwitch } = useSystemSwitchData();

    const checkAvailableStock = useCheckVariantConfiguratorAvailableStock();

    const { variantConfigurator } = useVariantConfigurator();

    const onDefaultSelectedChanged = useCallback(
        (selectedIndex, value) => {
            if (value) {
                setValues({
                    ...values,
                    packageBlockInput: {
                        ...values.packageBlockInput,
                        packageSettings: values.packageBlockInput.packageSettings.map((item, index) => ({
                            ...item,
                            // Set only one packageSetting's DefaultSelected to true
                            defaultSelected: index === selectedIndex,
                        })),
                    },
                });
            }
        },
        [setValues, values]
    );

    const handleRemove = useCallback(async () => {
        if (!packageSetting.id) {
            remove(index);
        } else {
            const settingDeletable = await checkAvailableStock(packageSetting.id, variantConfigurator.id);

            if (isNil(settingDeletable.data.inventory)) {
                remove(index);
            } else {
                message.warn(
                    t('variantConfiguratorDetails:messages.reservedStock', { name: packageSetting.packageName }),
                    3
                );
            }
        }
    }, [checkAvailableStock, index, packageSetting.id, packageSetting.packageName, remove, t, variantConfigurator.id]);

    return (
        <Card
            extra={
                total > 1 &&
                !disabled && <Button icon={<DeleteOutlined />} onClick={handleRemove} size="small" type="link" danger />
            }
            title={t('variantConfiguratorDetails:sections.packages.setting', { number: index + 1 })}
        >
            <Row gutter={16}>
                <Col lg={8} xs={24}>
                    <FormFields.TranslatedInputField
                        {...t('variantConfiguratorDetails:fields.packages.packageName', {
                            returnObjects: true,
                        })}
                        disabled={disabled}
                        name={`${name}[${index}]packageName`}
                        required
                    />
                </Col>
                <Col lg={8} xs={24}>
                    <FormFields.SelectField
                        {...t('variantConfiguratorDetails:fields.packages.packageType', {
                            returnObjects: true,
                        })}
                        disabled={disabled}
                        name={`${name}[${index}]packageType.type`}
                        options={configurator.packageOptions}
                        required
                        showSearch
                    />
                </Col>
                {fieldType.value === PackageKind.Price && (
                    <PackagePriceTypeFieldGroup disabled={disabled} name={`${name}[${index}]`} />
                )}
                {fieldType.value === PackageKind.Description && (
                    <PackageDescriptionTypeFieldGroup disabled={disabled} name={`${name}[${index}]`} />
                )}
                <Col lg={8} xs={24}>
                    <FormFields.SwitchField
                        {...t('variantConfiguratorDetails:fields.packages.defaultSelected', { returnObjects: true })}
                        {...yesNoSwitch}
                        disabled={disabled}
                        name={`${name}[${index}]defaultSelected`}
                        onChange={value => onDefaultSelectedChanged(index, value)}
                    />
                </Col>
            </Row>
            <Row gutter={16}>
                <Col lg={8} xs={24}>
                    <ImageUploadField
                        {...t('variantConfiguratorDetails:fields.packages.sectionImage', { returnObjects: true })}
                        disabled={disabled}
                        extensions={[...allowedExtensions.image, ...allowedExtensions.video]}
                        height={200}
                        imageFit="contain"
                        name={`${name}[${index}]sectionImage`}
                        sizeLimitInMiB={10}
                        required
                    />
                </Col>
                <Col lg={16} xs={24}>
                    <TranslatedInputFieldArray
                        {...t('variantConfiguratorDetails:fields.packages.features', { returnObjects: true })}
                        disabled={disabled}
                        name={`${name}[${index}]features`}
                        showAdd={!disabled}
                    />
                </Col>
                <Col lg={24} xs={24}>
                    <PackageAdditionalDetailFieldGroup
                        disabled={disabled}
                        name={`${name}[${index}]additionalDetails`}
                    />
                </Col>
            </Row>
        </Card>
    );
};

const PackageSettingFieldGroup = ({ disabled = false }: { disabled?: boolean }) => {
    const { t } = useTranslation(['variantConfiguratorDetails']);

    return (
        <ArrayField<PackageSettingValues, PackageBlockFormValues>
            name="packageBlockInput.packageSettings"
            render={({ name, push: pushSetting, remove }, packageSettings) => (
                <Space direction="vertical" size={14} style={{ width: '100%' }}>
                    {packageSettings?.map((_, settingIndex) => (
                        <PackageSettingItem
                            key={`${settingIndex.toString()}`}
                            disabled={disabled}
                            index={settingIndex}
                            name={name}
                            packageSetting={packageSettings[settingIndex]}
                            remove={remove}
                            total={packageSettings?.length}
                        />
                    ))}
                    {!disabled && (
                        <Button
                            icon={<PlusOutlined />}
                            onClick={() => pushSetting(getDefaultPackageSetting())}
                            type="primary"
                        >
                            {t('variantConfiguratorDetails:actions.packages.addSetting')}
                        </Button>
                    )}
                </Space>
            )}
        />
    );
};
export default PackageSettingFieldGroup;
