import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Space, Table, Typography, message } from 'antd';
import { isNil } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import { allowedExtensions } from '../../../../../server/utils/extensions';
import type { TrimBlockSpecsFragment } from '../../../../api/fragments/TrimBlockSpecs';
import ArrayField from '../../../../components/fields/ArrayField';
import ColorPickerField from '../../../../components/fields/ColorPickerField';
import CompanyCurrencyField from '../../../../components/fields/CompanyCurrencyField';
import FormItem from '../../../../components/fields/FormItem';
import SingleUploadField from '../../../../components/fields/SingleUploadField';
import FormFields from '../../../../themes/admin/Fields/FormFields';
import { useThemeComponents } from '../../../../themes/hooks';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import { useVariantConfigurator } from '../VariantConfiguratorContext';
import useCheckVariantConfiguratorAvailableStock from '../useCheckVariantConfiguratorAvailableStock';
import type { ColorBlockFormValues, ColorSettingFormValues } from './utils';
import { getDefaultColorSetting } from './utils';

const getRowFieldName = (parentName: string, index: number, childName: string) =>
    `${parentName}.[${index}].${childName}`;

type ColorBlockSettingFieldsProps = {
    name: string;
    trimBlock: TrimBlockSpecsFragment;
    disabled?: boolean;
};

const ColorBlockSettingField = ({ name, trimBlock, disabled = false }: ColorBlockSettingFieldsProps) => {
    const { t } = useTranslation(['variantConfiguratorDetails']);
    const { ResponsiveStyledTable } = useThemeComponents();

    const { variantConfigurator } = useVariantConfigurator();
    const checkAvailableStock = useCheckVariantConfiguratorAvailableStock();

    const removeSetting = async (
        settingId: string,
        name: string,
        rowIndex: number,
        remove: (rowIndex: number) => ColorSettingFormValues
    ) => {
        const settingDeletable = await checkAvailableStock(settingId, variantConfigurator.id);
        if (isNil(settingDeletable.data.inventory)) {
            remove(rowIndex);
        } else {
            message.warn(t('variantConfiguratorDetails:messages.reservedStock', { name }), 3);
        }
    };
    const translate = useTranslatedString();

    return (
        <ArrayField<ColorSettingFormValues, ColorBlockFormValues>
            name={name}
            render={({ push, remove }, colorSettings) => (
                <Space direction="vertical" style={{ width: '100%', marginTop: -16 }}>
                    <ResponsiveStyledTable
                        dataSource={colorSettings}
                        pagination={false}
                        rowKey={(record, index) => `${index.toString()}`}
                        outLineBordered
                    >
                        <Table.Column
                            dataIndex="name"
                            render={(_, __, rowIndex: number) => (
                                <FormFields.TranslatedInputField
                                    disabled={disabled}
                                    name={getRowFieldName(name, rowIndex, 'name')}
                                    {...t('variantConfiguratorDetails:fields.colors.settings.name', {
                                        returnObjects: true,
                                    })}
                                />
                            )}
                            title={
                                <FormItem
                                    label={t('variantConfiguratorDetails:fields.colors.settings.name.label')}
                                    required
                                />
                            }
                            width={400}
                        />
                        <Table.Column
                            dataIndex="sectionImages"
                            render={(images: ColorSettingFormValues['sectionImages'], __, rowIndex: number) =>
                                images?.map((sectionImage, index) => (
                                    <div key={`${sectionImage?.trimId}_${rowIndex.toString()}_${index.toString()}`}>
                                        <Typography.Text
                                            ellipsis={{ tooltip: translate(sectionImage?.trimName) }}
                                            style={{ width: 94 }}
                                        >
                                            {translate(sectionImage?.trimName)}
                                        </Typography.Text>
                                        <SingleUploadField
                                            disabled={disabled}
                                            extensions={allowedExtensions.image}
                                            listType="picture-card"
                                            name={getRowFieldName(
                                                name,
                                                rowIndex,
                                                `sectionImages[${index}].variantColorImage`
                                            )}
                                            size="small"
                                            style={{
                                                marginBottom: 0,
                                            }}
                                        />
                                    </div>
                                ))
                            }
                            title={
                                <FormItem
                                    label={t('variantConfiguratorDetails:fields.colors.settings.sectionImage.label')}
                                    required={trimBlock?.trimSettings?.length > 0}
                                />
                            }
                            width={140}
                        />
                        <Table.Column
                            dataIndex="code"
                            render={(_, __, rowIndex: number) => (
                                <FormFields.InputField
                                    disabled={disabled}
                                    name={getRowFieldName(name, rowIndex, 'code')}
                                />
                            )}
                            title={
                                <FormItem label={t('variantConfiguratorDetails:fields.colors.settings.code.label')} />
                            }
                            width={150}
                        />
                        <Table.Column
                            dataIndex="image"
                            render={(_, __, rowIndex: number) => (
                                <SingleUploadField
                                    disabled={disabled}
                                    extensions={allowedExtensions.image}
                                    listType="picture-card"
                                    name={getRowFieldName(name, rowIndex, 'image')}
                                    size="small"
                                    style={{
                                        marginTop: 'calc(1rem + 2px)',
                                        marginBottom: 0,
                                    }}
                                />
                            )}
                            title={
                                <FormItem label={t('variantConfiguratorDetails:fields.colors.settings.image.label')} />
                            }
                        />
                        <Table.Column
                            dataIndex="hex"
                            render={(_, __, rowIndex: number) => (
                                <ColorPickerField disabled={disabled} name={getRowFieldName(name, rowIndex, 'hex')} />
                            )}
                            title={
                                <FormItem
                                    label={t('variantConfiguratorDetails:fields.colors.settings.hex.label')}
                                    required
                                />
                            }
                            width={200}
                        />
                        <Table.Column
                            dataIndex="price"
                            render={(_, __, rowIndex: number) => (
                                <CompanyCurrencyField
                                    company={variantConfigurator.company}
                                    disabled={disabled}
                                    name={getRowFieldName(name, rowIndex, 'price')}
                                />
                            )}
                            title={
                                <FormItem label={t('variantConfiguratorDetails:fields.colors.settings.price.label')} />
                            }
                            width={200}
                        />
                        {colorSettings?.length > 1 && (
                            <Table.Column
                                dataIndex="id"
                                render={(_, __, rowIndex: number) =>
                                    !disabled && (
                                        <Button
                                            icon={<DeleteOutlined />}
                                            onClick={() =>
                                                removeSetting(
                                                    colorSettings[rowIndex].id,
                                                    colorSettings[rowIndex].name.defaultValue,
                                                    rowIndex,
                                                    remove
                                                )
                                            }
                                            size="small"
                                            style={{ marginBottom: 16 }}
                                            type="link"
                                            danger
                                        />
                                    )
                                }
                                width={60}
                            />
                        )}
                    </ResponsiveStyledTable>
                    {!disabled && (
                        <Button
                            icon={<PlusOutlined />}
                            onClick={() => push(getDefaultColorSetting(trimBlock))}
                            type="primary"
                        >
                            {t('variantConfiguratorDetails:actions.colors.addSetting')}
                        </Button>
                    )}
                </Space>
            )}
        />
    );
};

export default ColorBlockSettingField;
