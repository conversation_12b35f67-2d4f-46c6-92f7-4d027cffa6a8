import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, message } from 'antd';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../shared/permissions';
import { useGetModulesOptionsQuery } from '../../../api/queries/getModulesOptions';
import type { ModuleFilteringRule } from '../../../api/types';
import { ModuleType } from '../../../api/types';
import { useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useExcelUploadModal } from '../../../components/vehicleImportExport';
import { ImportExportType, useModuleSelectModal } from '../../../components/vehicleImportExport/ModuleSelectModal';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import { exportVehicles } from '../../../utilities/export';
import { VehicleExcelDownloadType } from '../../../utilities/export/vehicles';
import hasPermissions from '../../../utilities/hasPermissions';
import useGoTo from '../../../utilities/useGoTo';
import ModelList from './ModelList';

const ModelListPage = () => {
    const { t } = useTranslation('modelList');
    const { token } = useAccountContext();
    const company = useCompany(true);
    const { id: companyId, displayName, countryCode } = company || {};

    const excelUploadModal = useExcelUploadModal(VehicleExcelDownloadType.Models);
    const moduleSelectUploadModal = useModuleSelectModal(VehicleExcelDownloadType.Models);

    const vehicleModule = useMemo(
        (): ModuleFilteringRule => ({
            moduleType: ModuleType.SimpleVehicleManagement,
            companyId,
        }),
        [companyId]
    );

    const { data, loading } = useGetModulesOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { ...vehicleModule } },
    });

    const goToNewModelPage = useGoTo('/admin/vehicles/models/add');

    const showUploadExcelModal = useCallback(() => {
        if (!company) {
            message.warn('Please select a company before uploading.');
        } else if (data?.modules?.items?.length > 1) {
            moduleSelectUploadModal.open(ImportExportType.Import);
        } else {
            excelUploadModal.open(data?.modules?.items[0].id);
        }
    }, [company, excelUploadModal, moduleSelectUploadModal, data?.modules?.items]);

    const downloadExcel = () => {
        if (!company) {
            message.warn('Please select a company before downloading.');
        } else if (data?.modules?.items?.length > 1) {
            moduleSelectUploadModal.open(ImportExportType.Export);
        } else {
            exportVehicles(
                displayName,
                countryCode,
                VehicleExcelDownloadType.Models,
                false,
                data?.modules?.items[0].id,
                token
            );
        }
    };

    const hasPermissionsToCreate = useMemo(() => {
        if (data?.modules?.items?.length) {
            return data.modules.items.some(
                module =>
                    module.__typename === 'SimpleVehicleManagementModule' &&
                    hasPermissions(module.permissions, [permissionKind.createVehicle])
            );
        }

        return false;
    }, [data?.modules]);

    if (!data?.modules?.items || loading) {
        return null;
    }

    const extra = (
        <>
            {hasPermissionsToCreate && (
                <Button icon={<PlusOutlined />} onClick={goToNewModelPage} type="primary">
                    {t('modelList:actions.addModel')}
                </Button>
            )}
            {company?.displayName !== 'BMW' &&
                hasPermissions(company?.permissions, [permissionKind.manageVehicles]) && (
                    <>
                        <Button icon={<UploadOutlined />} onClick={showUploadExcelModal} type="primary">
                            {t('modelList:actions.importExcel')}
                        </Button>
                        <Button icon={<DownloadOutlined />} onClick={downloadExcel} type="primary">
                            {t('modelList:actions.exportExcel')}
                        </Button>
                    </>
                )}
        </>
    );

    return (
        <ConsolePageWithHeader extra={extra} title={t('modelList:title')}>
            <ModelList />
            {excelUploadModal.render()}
            {moduleSelectUploadModal.render()}
        </ConsolePageWithHeader>
    );
};

export default ModelListPage;
