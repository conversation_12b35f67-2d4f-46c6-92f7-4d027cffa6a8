import { SearchOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Table } from 'antd';
import { pick } from 'lodash/fp';
import { useMemo, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { CompanyListDataFragment } from '../../../api/fragments/CompanyListData';
import { useListCompaniesQuery } from '../../../api/queries/listCompanies';
import { CompanySortingField } from '../../../api/types';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import SearchBox from '../../../components/SearchBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import makeGetSortingRule from '../../../utilities/makeGetSortingRule';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import useListReducer from './useListReducer';

const renderFilterIcon = (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />;

const getSortField = makeGetSortingRule((field): CompanySortingField => {
    switch (field) {
        case 'companyName':
            return CompanySortingField.Name;

        case 'currency':
            return CompanySortingField.Currency;

        default:
            throw new Error('Unknown Sorting Field!');
    }
});

const CompanyList = () => {
    const { t } = useTranslation('companyList');

    const navigate = useNavigate();

    const inputRef = useRef<InputRef>(null);

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Company);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListCompaniesQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { ...filter, companyId: company?.id },
        },
    });

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            name: !filters.companyName ? null : filters.companyName?.[0],
                            currency: !filters.currency ? null : filters.currency[0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // prepare items as a data source
    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    const showFilterDropDown = useCallback(
        (props, dataIndex) =>
            SearchBox({
                filterDropDownProps: props,
                dataIndex,
                onRef: node => {
                    inputRef.current = node;
                },
            }),
        [inputRef]
    );

    const onFilterDropdownVisibleChange = useCallback(
        visible => {
            if (visible) {
                setTimeout(() => inputRef.current.select(), 100);
            }
        },
        [inputRef]
    );

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: CompanyListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/companies/${row.id}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            state={state}
            tableName={t('companyList:title')}
            total={total}
        >
            <Table.Column
                key="companyName"
                dataIndex="companyName"
                filterDropdown={props => showFilterDropDown(props, 'companyName')}
                filterIcon={renderFilterIcon}
                filteredValue={filter.name ? [filter.name] : undefined}
                onFilterDropdownVisibleChange={onFilterDropdownVisibleChange}
                render={item => item.defaultValue}
                sortOrder={sort?.field === CompanySortingField.Name ? sort.orderValue : undefined}
                title={t('companyList:columns.displayName')}
                sorter
            />
            <Table.Column
                key="currency"
                dataIndex="currency"
                sortOrder={sort?.field === CompanySortingField.Currency ? sort.orderValue : undefined}
                title={t('companyList:columns.currency')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                title={t('companyList:columns.isActive')}
                width={120}
            />
        </PaginatedTableWithContext>
    );
};

export default CompanyList;
