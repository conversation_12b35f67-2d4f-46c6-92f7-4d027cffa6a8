import { Table, Switch, message } from 'antd';
import FormItem from 'antd/lib/form/FormItem';
import isEqual from 'fast-deep-equal';
import { useField } from 'formik';
import type { Key } from 'react';
import { useCallback, useMemo, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import type { LocalCustomerManagementModuleSpecsFragment } from '../../../api/fragments/LocalCustomerManagementModuleSpecs';
import type { KycFieldPurpose, KycFieldSettings } from '../../../api/types';
import { LocalCustomerFieldKey } from '../../../api/types';
import { Select } from '../../../components/fields/SelectField';
import { useThemeComponents } from '../../../themes/hooks';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import useKycPresetOptions from '../../../utilities/useKycPresetOptions';
import type { KYCFieldDefinition } from './KYCFieldDefinitions';
import { useKycFieldDefinitionsFromCountry } from './KYCFieldDefinitions';
import useListReducer from './useListReducer';
import useRenderKYCFieldName from './useRenderKYCFieldName';

type KYCFieldsTableProps = {
    name: string;
    module: LocalCustomerManagementModuleSpecsFragment;
};

type State = Array<KYCFieldDefinition & { isSelected: boolean; isRequired: boolean; purpose: KycFieldPurpose[] }>;

const KYCFieldsTable = ({ name, module }: KYCFieldsTableProps) => {
    const { t } = useTranslation('localCustomerModuleDetails');
    const { Table: StyledTable } = useThemeComponents();
    const { purposeOptions } = useKycPresetOptions();

    // state to be persisted on formik
    const [{ value: initialValues }, { error: fieldError }, { setValue }] = useField<KycFieldSettings[]>(name);

    const initialStateRef = useRef<State | null>(null);

    const definitions = useKycFieldDefinitionsFromCountry(module.company.countryCode);

    const moduleDefinitions = useMemo(
        () => definitions.filter(({ key }) => module.kycFields.some(item => item.field === key)),
        [module.kycFields, definitions]
    );

    if (initialStateRef.current === null) {
        initialStateRef.current = moduleDefinitions.map(item => {
            const initialValue = initialValues.find(field => field.key === item.key);

            return {
                ...item,
                isSelected: !!initialValue,
                isRequired: !!initialValue?.isRequired,
                purpose: initialValue?.purpose,
            };
        });
    }
    // local state
    const [state, dispatch] = useListReducer(initialStateRef.current);

    const { selectedKeys, currentValue } = useMemo(
        () =>
            state.reduce<{ selectedKeys: LocalCustomerFieldKey[]; currentValue: KycFieldSettings[] }>(
                (acc, item) => {
                    if (!item.isSelected) {
                        return acc;
                    }

                    return {
                        selectedKeys: [...acc.selectedKeys, item.key],
                        currentValue: [
                            ...acc.currentValue,
                            { key: item.key, isRequired: item.isRequired, purpose: item.purpose },
                        ],
                    };
                },
                {
                    selectedKeys: [],
                    currentValue: [],
                }
            ),
        [state]
    );

    const previousValuesRef = useRef(currentValue);

    useEffect(() => {
        if (!isEqual(previousValuesRef.current, currentValue)) {
            previousValuesRef.current = currentValue;
            setValue(currentValue);
        }
    }, [currentValue, setValue, previousValuesRef]);

    const onKeySelected = useCallback(
        (keys: Key[]) => {
            const hasTitleAndSalutation =
                keys.includes(LocalCustomerFieldKey.Title) &&
                keys.includes(LocalCustomerFieldKey.Salutation || LocalCustomerFieldKey.SalutationBmw);

            const hasBirthdayAndDrivingLicense =
                keys.includes(LocalCustomerFieldKey.UaeDrivingLicense) && keys.includes(LocalCustomerFieldKey.Birthday);
            if (hasTitleAndSalutation) {
                message.warn('KYC fields cannot have both Title and Salutation!');

                return;
            }
            if (hasBirthdayAndDrivingLicense) {
                message.warn('KYC fields cannot have both Birthday and DrivingLicense');

                return;
            }
            dispatch({ type: 'selectFields', keys: keys as LocalCustomerFieldKey[] });
        },
        [dispatch]
    );

    const renderRadioGroup = useCallback(
        (record: State[number]) => (
            <Switch
                key={record.key}
                checked={record.isRequired}
                checkedChildren="ON"
                disabled={!record.isSelected}
                onChange={value => {
                    dispatch({ type: 'setRequired', key: record.key, value });
                }}
                unCheckedChildren="OFF"
            />
        ),
        [dispatch]
    );

    const getPurposeFieldError = useCallback(
        (record: State[number]) => {
            const fieldErrorIndex = currentValue.findIndex(err => err.key === record.key);

            if (fieldErrorIndex === -1 || !fieldError) {
                return null;
            }

            if (Array.isArray(fieldError) && fieldError[fieldErrorIndex]?.purpose) {
                return fieldError[fieldErrorIndex]?.purpose;
            }

            return null;
        },
        [currentValue, fieldError]
    );

    const renderPurpose = useCallback(
        (record: State[number]) => {
            const errorMessage = getPurposeFieldError(record);

            return (
                <>
                    <Select
                        key={record.key}
                        disabled={!record.isSelected}
                        mode="multiple"
                        onChange={(value: KycFieldPurpose[]) => {
                            dispatch({ type: 'setPurpose', key: record.key, value });
                        }}
                        options={purposeOptions}
                        status={errorMessage ? 'error' : undefined}
                        style={{ width: '100%' }}
                        value={record.purpose}
                        showArrow
                    />
                    {errorMessage && <div className="ant-form-item-explain-error">{errorMessage}</div>}
                </>
            );
        },
        [dispatch, getPurposeFieldError, purposeOptions]
    );

    const renderKYCFieldName = useRenderKYCFieldName();

    return (
        <FormItem name={name}>
            <StyledTable
                dataSource={state}
                rowKey="key"
                rowSelection={{
                    type: 'checkbox',
                    selectedRowKeys: selectedKeys,
                    onChange: onKeySelected,
                }}
                size="middle"
            >
                <Table.Column
                    dataIndex="key"
                    render={key => renderKYCFieldName(key)}
                    title={t('localCustomerModuleDetails:kycPresetsDrawer.columns.key')}
                />
                <Table.Column
                    render={renderRadioGroup}
                    title={t('localCustomerModuleDetails:kycPresetsDrawer.columns.isRequired')}
                />
                <Table.Column
                    render={renderPurpose}
                    title={t('localCustomerModuleDetails:kycPresetsDrawer.columns.placement')}
                    width={300}
                />
                <Table.Column
                    align="center"
                    dataIndex="availableOnUserInputs"
                    render={value => renderBooleanIcon(value, true)}
                    title={t('localCustomerModuleDetails:kycPresetsDrawer.columns.availableOnUserInputs')}
                    width={120}
                />
                {module.company.countryCode === 'SG' && (
                    <Table.Column
                        align="center"
                        dataIndex="availableOnMyInfo"
                        render={value => renderBooleanIcon(value, true)}
                        title={t('localCustomerModuleDetails:kycPresetsDrawer.columns.availableOnMyInfo')}
                        width={120}
                    />
                )}
            </StyledTable>
        </FormItem>
    );
};

export default KYCFieldsTable;
