import { useApolloClient } from '@apollo/client';
import { Button, Form, message, Space } from 'antd';
import { Formik } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import {
    CreateRouterDocument,
    type CreateRouterMutation,
    type CreateRouterMutationVariables,
} from '../../../api/mutations/createRouter';
import { useListCompaniesForSelectionQuery } from '../../../api/queries/listCompaniesForSelection';
import { LayoutType } from '../../../api/types';
import DrawerWithAutoWidth from '../../../components/DrawerWithAutoWidth';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import CheckboxField from '../../../components/fields/CheckboxField';
import InputField from '../../../components/fields/InputField';
import SelectField from '../../../components/fields/SelectField';
import { useRuntimeConfig } from '../../../runtimeConfig';
import useHandleError from '../../../utilities/useHandleError';
import useSystemOptions from '../../../utilities/useSystemOptions';

export type RouterCreationDrawerProps = {
    visible: boolean;
    onClose: () => void;
};

type FormValues = CreateRouterMutationVariables['settings'] & { companyId: string | null };

const RouterCreationDrawer = ({ onClose, visible }: RouterCreationDrawerProps) => {
    const {
        router: { hostname },
    } = useRuntimeConfig();
    const { t } = useTranslation(['routerList', 'routerDetails']);
    const apolloClient = useApolloClient();
    const navigate = useNavigate();
    const { layoutOptions } = useSystemOptions();

    // get company context
    const company = useCompany(true);

    const initialValues = useMemo(
        (): FormValues => ({
            hostname,
            pathname: '/',
            withAdmin: false,
            companyId: null,
            languages: [],
            layoutType: LayoutType.BasicPro,
        }),
        [hostname]
    );

    const onSubmit = useHandleError(
        async ({ companyId, ...settings }: FormValues) => {
            if (!companyId) {
                throw new Error('missing company ID');
            }

            message.loading({
                content: t('routerList:creationDrawer.messages.submitting'),
                duration: 0,
                key: 'primary',
            });

            const { data } = await apolloClient
                .mutate<CreateRouterMutation, CreateRouterMutationVariables>({
                    mutation: CreateRouterDocument,
                    variables: { companyId, settings },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            message.success({
                content: t('routerList:creationDrawer.messages.success'),
                key: 'primary',
            });

            // navigate to new router
            navigate(`/admin/system/routers/${data.router.id}`);
        },
        [apolloClient, t, navigate]
    );

    const { data } = useListCompaniesForSelectionQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { companyId: company?.id, withRouterCreation: true } },
    });

    const companies = data?.list?.items || [];

    return (
        <DrawerWithAutoWidth
            extra={
                <Space>
                    <Button onClick={onClose}>{t('routerList:creationDrawer.cancelButton')}</Button>
                    <Button form="createRouter" htmlType="submit" type="primary">
                        {t('routerList:creationDrawer.submitButton')}
                    </Button>
                </Space>
            }
            onClose={onClose}
            open={visible}
            title={t('routerList:creationDrawer.title')}
            destroyOnClose
        >
            <Formik initialValues={initialValues} onSubmit={onSubmit}>
                {({ handleSubmit }) => (
                    <Form id="createRouter" layout="vertical" name="createRouter" onSubmitCapture={handleSubmit}>
                        <SelectField
                            {...t('routerDetails:fields.company', { returnObjects: true })}
                            name="companyId"
                            options={companies.map(company => ({ value: company.id, label: company.displayName }))}
                            required
                            showSearch
                        />
                        <SelectField
                            {...t('routerDetails:fields.layoutType', { returnObjects: true })}
                            name="layoutType"
                            options={layoutOptions}
                            required
                            showSearch
                        />
                        <InputField {...t('routerDetails:fields.hostname', { returnObjects: true })} name="hostname" />
                        <InputField {...t('routerDetails:fields.pathname', { returnObjects: true })} name="pathname" />
                        <CheckboxField
                            {...t('routerDetails:fields.withAdmin', { returnObjects: true })}
                            name="withAdmin"
                        />
                        <InputField
                            {...t('routerDetails:fields.googleTagManagerId', { returnObjects: true })}
                            name="googleTagManagerId"
                        />
                        <InputField {...t('routerDetails:fields.pixelId', { returnObjects: true })} name="pixelId" />
                    </Form>
                )}
            </Formik>
        </DrawerWithAutoWidth>
    );
};

export default RouterCreationDrawer;

export const useRouterCreationDrawer = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: () => <RouterCreationDrawer onClose={actions.close} visible={visible} />,
    };
};
