import { Table } from 'antd';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { isEmpty } from 'lodash';
import { pick } from 'lodash/fp';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { FinanceProductListDataFragment } from '../../../api/fragments/FinanceProductListData';
import { useListFinanceProductsQuery } from '../../../api/queries/listFinanceProducts';
import { CompanyFilterListCollection } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import useSearchInDropDown from '../../../components/useSearchInDropDown';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useFormatFinanceProductType from '../../../utilities/useFormatFinanceProductType';
import useGetCompanyFilter from '../../../utilities/useGetCompanyFilter';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import FinanceProductEmptyListResult from './FinanceProductEmptyListResult';
import useListReducer from './useListReducer';
import useSortOrder, { getSortField } from './useSortOrder';

const FinanceProductList = () => {
    const { t } = useTranslation('financeProductList');
    const formatFinanceProductType = useFormatFinanceProductType();

    const searchBox = useSearchInDropDown();

    const navigate = useNavigate();

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.FinanceProduct);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListFinanceProductsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { companyIds: company ? [company.id] : undefined, ...filter },
        },
    });

    const companyFilterList = useGetCompanyFilter({ collection: CompanyFilterListCollection.FinanceProducts });

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            displayName: !filters.displayName ? null : filters.displayName[0],
                            identifier: !filters.identifier ? null : filters.identifier[0],
                            bankName: !filters.bank ? null : filters.bank[0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // prepare items as a data source
    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    const {
        companySort,
        identifierSort,
        displayNameSort,
        financeProductTypeSort,
        bankSort,
        startDateSort,
        endDateSort,
        orderSort,
    } = useSortOrder(sort);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (total === 0 && isEmpty(filter)) {
            return <FinanceProductEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: FinanceProductListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/financeProducts/${row.versioning.suiteId}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            scroll={{ x: true }}
            state={state}
            tableName={t('financeProductList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="company"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, companyFilterList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.companyIds}
                    sortOrder={companySort}
                    title={t('financeProductList:columns.company')}
                    sorter
                />
            )}
            <Table.Column
                key="identifier"
                dataIndex="identifier"
                filterDropdown={searchBox.render(t('financeProductList:columns.identifier'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.identifier ? [filter.identifier] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={identifierSort}
                title={t('financeProductList:columns.identifier')}
                fixed
                sorter
            />
            <Table.Column
                key="displayName"
                dataIndex="displayName"
                filterDropdown={searchBox.render(t('financeProductList:columns.displayName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.displayName ? [filter.displayName] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={displayNameSort}
                title={t('financeProductList:columns.displayName')}
                sorter
            />
            <Table.Column
                key="type"
                dataIndex="type"
                render={value => formatFinanceProductType(value)}
                sortOrder={financeProductTypeSort}
                title={t('financeProductList:columns.type')}
                sorter
            />
            <Table.Column
                key="bank"
                dataIndex={['bank', 'displayName']}
                filterDropdown={searchBox.render(t('financeProductList:columns.bank'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.bankName ? [filter.bankName] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={bankSort}
                title={t('financeProductList:columns.bank')}
                sorter
            />
            <Table.Column
                key="start"
                dataIndex={['period', 'start']}
                render={(value, record: FinanceProductListDataFragment) =>
                    t('common:formats.date', { date: new Date(value) })
                }
                sortOrder={startDateSort}
                title={t('financeProductList:columns.startDate')}
                sorter
            />
            <Table.Column
                key="end"
                dataIndex={['period', 'end']}
                render={(value, record: FinanceProductListDataFragment) =>
                    t('common:formats.date', { date: new Date(value) })
                }
                sortOrder={endDateSort}
                title={t('financeProductList:columns.endDate')}
                sorter
            />
            <Table.Column
                key="order"
                dataIndex="order"
                sortOrder={orderSort}
                title={t('financeProductList:columns.order')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                title={t('financeProductList:columns.isActive')}
                width={120}
            />
        </PaginatedTableWithContext>
    );
};

export default FinanceProductList;
