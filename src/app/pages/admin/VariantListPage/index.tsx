import { DownloadOutlined, PlusOutlined, SyncOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, message, Modal } from 'antd';
import { isEmpty } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../shared/permissions';
import { useSynchronizePorscheMasterDataMutation } from '../../../api/mutations/synchronizePorscheMasterData';
import { useGetModulesOptionsQuery } from '../../../api/queries/getModulesOptions';
import type { ModuleFilteringRule } from '../../../api/types';
import { ModuleType } from '../../../api/types';
import { useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useExcelUploadModal } from '../../../components/vehicleImportExport';
import { ImportExportType, useModuleSelectModal } from '../../../components/vehicleImportExport/ModuleSelectModal';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import { exportVehicles } from '../../../utilities/export';
import { VehicleExcelDownloadType } from '../../../utilities/export/vehicles';
import hasPermissions from '../../../utilities/hasPermissions';
import renderMarkdown from '../../../utilities/renderMarkdown';
import useGoTo from '../../../utilities/useGoTo';
import ImportImages from './ImportImages';
import VariantList from './VariantList';
import VehicleModuleSelectorModal from './VehicleModuleSelectorModal';

const useSynchronizePorscheMasterData = (companyId: string) => {
    const { t } = useTranslation(['variantList']);
    const [sync] = useSynchronizePorscheMasterDataMutation();

    return useCallback(async () => {
        if (isEmpty(companyId)) {
            return;
        }

        // submitting message
        message.loading({
            content: t('variantList:messages.synchronizing'),
            key: 'primary',
            duration: 0,
        });

        try {
            const { data } = await sync({ variables: { companyId } });
            const result = data?.result;

            const lines: string[] = [];
            const pushSection = (items: string[] | null | undefined, title: string) => {
                if (!isEmpty(items)) {
                    lines.push(title);
                    lines.push(...(items ?? []).map(msg => `- ${msg}`));
                    lines.push('');
                }
            };

            pushSection(result?.errors, t('variantList:messages.synchronizeResult.errors'));
            pushSection(result?.created?.make, t('variantList:messages.synchronizeResult.createdMake'));
            pushSection(result?.created?.model, t('variantList:messages.synchronizeResult.createdModel'));
            pushSection(result?.created?.variant, t('variantList:messages.synchronizeResult.createdVariant'));
            pushSection(result?.updated, t('variantList:messages.synchronizeResult.updated'));

            const hasChange = lines.length > 0;

            if (!hasChange) {
                lines.push(t('variantList:messages.synchronizeResult.noChange'));
            }

            Modal.info({
                className: 'static-modal',
                title: t('variantList:messages.synchronizeResult.title'),
                content: isEmpty(result?.errors) ? (
                    <div>
                        {hasChange && <p>{t('variantList:messages.synchronizeResult.summary')}</p>}
                        {renderMarkdown(lines.join('\r\n'))}
                        {hasChange && t('variantList:messages.synchronizeResult.note')}
                    </div>
                ) : (
                    renderMarkdown(lines.join('\r\n'))
                ),
                onOk: () => {
                    window.location.reload();
                },
            });
        } catch (error) {
            console.error(error);

            message.error({ content: t('variantList:errors.unknown') });
        } finally {
            message.destroy('primary');
        }
    }, [companyId, sync, t]);
};

const VariantListPage = () => {
    const { t } = useTranslation('variantList');
    const { token } = useAccountContext();
    const company = useCompany(true);
    const { displayName, countryCode } = company || {};

    const filter = useMemo(
        (): ModuleFilteringRule => ({ moduleType: ModuleType.SimpleVehicleManagement, companyId: company?.id }),
        [company]
    );
    const { data, loading } = useGetModulesOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { ...filter, companyId: company?.id } },
    });

    const excelUploadModal = useExcelUploadModal(VehicleExcelDownloadType.Variants);
    const moduleSelectUploadModal = useModuleSelectModal(VehicleExcelDownloadType.Variants);

    const goToNewVariantPage = useGoTo('/admin/vehicles/variants/add');

    const showUploadExcelModal = useCallback(() => {
        if (!company) {
            message.warn('Please select a company before uploading.');
        } else if (data?.modules?.items?.length > 1) {
            moduleSelectUploadModal.open(ImportExportType.Import);
        } else {
            excelUploadModal.open(data?.modules?.items[0].id);
        }
    }, [company, excelUploadModal, moduleSelectUploadModal, data?.modules?.items]);

    const downloadExcel = useCallback(async () => {
        if (!company) {
            message.warn('Please select a company before uploading.');
        } else if (data?.modules?.items?.length > 1) {
            moduleSelectUploadModal.open(ImportExportType.Export);
        } else {
            await exportVehicles(
                displayName,
                countryCode,
                VehicleExcelDownloadType.Variants,
                false,
                data?.modules?.items[0].id,
                token
            );
        }
    }, [company, data?.modules?.items, moduleSelectUploadModal, displayName, countryCode, token]);

    const synchronizePorscheMasterData = useSynchronizePorscheMasterData(company?.id);
    const showSyncButton = useMemo(
        () =>
            !isEmpty(company?.id) &&
            data?.modules?.items?.some(
                item => item?.__typename === 'SimpleVehicleManagementModule' && item?.porscheMasterDataModuleId
            ),
        [company?.id, data?.modules?.items]
    );

    const hasPermissionsToCreate = useMemo(() => {
        if (data?.modules?.items?.length) {
            return data.modules.items.some(
                module =>
                    module.__typename === 'SimpleVehicleManagementModule' &&
                    hasPermissions(module.permissions, [permissionKind.createVehicle])
            );
        }

        return false;
    }, [data?.modules]);

    if (!data?.modules?.items || loading) {
        return null;
    }

    const importImageButton = (length: number) => {
        if (length > 1) {
            return <VehicleModuleSelectorModal />;
        }

        return <ImportImages moduleId={data.modules.items[0].id} />;
    };

    const extra = (
        <>
            {hasPermissionsToCreate && (
                <Button icon={<PlusOutlined />} onClick={goToNewVariantPage} type="primary">
                    {t('variantList:actions.addVariant')}
                </Button>
            )}
            {hasPermissions(company?.permissions, [permissionKind.manageVehicles]) && (
                <>
                    <Button icon={<UploadOutlined />} onClick={showUploadExcelModal} type="primary">
                        {t('variantList:actions.importExcel')}
                    </Button>
                    <Button icon={<DownloadOutlined />} onClick={downloadExcel} type="primary">
                        {t('variantList:actions.exportExcel')}
                    </Button>
                    {data.modules.items.length ? importImageButton(data.modules.items.length) : null}
                    {showSyncButton && (
                        <Button icon={<SyncOutlined />} onClick={synchronizePorscheMasterData} type="primary">
                            {t('variantList:actions.synchronizePorscheMasterData')}
                        </Button>
                    )}
                </>
            )}
        </>
    );

    return (
        <ConsolePageWithHeader extra={extra} title={t('variantList:title')}>
            <VariantList />
            {excelUploadModal.render()}
            {moduleSelectUploadModal.render()}
        </ConsolePageWithHeader>
    );
};

export default VariantListPage;
