import { Table } from 'antd';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { isEmpty, pick } from 'lodash/fp';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { LocalMakesListDataFragment } from '../../../api/fragments/LocalMakesListData';
import { useListLocalMakesQuery } from '../../../api/queries/listLocalMakes';
import { CompanyFilterListCollection } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import useSearchInDropDown from '../../../components/useSearchInDropDown';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useGetCompanyFilter from '../../../utilities/useGetCompanyFilter';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import MakeEmptyListResult from './MakeEmptyListResult';
import useListReducer from './useListReducer';
import useSortOrder, { getSortField } from './useSortOrder';

const MakeList = () => {
    const { t } = useTranslation('makeList');

    // get company context
    const company = useCompany(true);

    const navigate = useNavigate();

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Make);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListLocalMakesQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { companyIds: company ? [company.id] : undefined, ...filter },
        },
    });

    const companyFilterList = useGetCompanyFilter({ collection: CompanyFilterListCollection.LocalMakes });

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            name: !filters.name ? null : { defaultValue: filters.name[0], overrides: [] },
                            identifier: !filters.identifier ? null : filters.identifier[0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // prepare items as a data source
    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);
    const total = data?.list?.count || 0;
    const searchBox = useSearchInDropDown();

    const { companySort, identifierSort, nameSort, orderSort, activeSort } = useSortOrder(sort);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (dataSource.length === 0 && isEmpty(filter)) {
            return <MakeEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: LocalMakesListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/vehicles/makes/${row.versioning.suiteId}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            state={state}
            tableName={t('makeList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="company"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, companyFilterList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.companyIds}
                    sortOrder={companySort}
                    title={t('makeList:columns.company')}
                    sorter
                />
            )}
            <Table.Column
                key="identifier"
                dataIndex="identifier"
                filterDropdown={searchBox.render('identifier')}
                filterIcon={renderSearchIcon}
                filteredValue={filter.identifier ? [filter.identifier] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={identifierSort}
                title={t('makeList:columns.identifier')}
                sorter
            />
            <Table.Column
                key="name"
                dataIndex="name"
                filterDropdown={searchBox.render('name')}
                filterIcon={renderSearchIcon}
                filteredValue={filter?.name?.defaultValue ? [filter?.name?.defaultValue] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                render={name => name.defaultValue}
                sortOrder={nameSort}
                title={t('makeList:columns.name')}
                sorter
            />
            <Table.Column
                key="order"
                dataIndex="order"
                sortOrder={orderSort}
                title={t('makeList:columns.order')}
                sorter
            />

            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                sortOrder={activeSort}
                title={t('makeList:columns.isActive')}
                width={120}
                sorter
            />
        </PaginatedTableWithContext>
    );
};

export default MakeList;
