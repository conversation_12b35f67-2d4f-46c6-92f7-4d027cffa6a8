import Table from 'antd/es/table';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { InsuranceProductListDataFragment } from '../../../api/fragments/InsuranceProductListData';
import { useListInsuranceProductsQuery } from '../../../api/queries/listInsuranceProducts';
import { CompanyFilterListCollection, Purpose } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import { useCompanies, useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import useSearchInDropDown from '../../../components/useSearchInDropDown';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useGetCompanyFilter from '../../../utilities/useGetCompanyFilter';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import InsuranceProductEmptyListResult from './InsuranceProductEmptyListResult';
import useListReducer from './useListReducer';
import useSortOrder, { getSortField } from './useSortOrder';

const InsuranceProductList = () => {
    const { t } = useTranslation('insuranceProductList');
    const searchBox = useSearchInDropDown();
    const navigate = useNavigate();

    // get company context
    const company = useCompany(true);
    const companies = useCompanies(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.InsuranceProduct);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    // Wrap the filter
    const wrappedFilter = useMemo(() => {
        let defaultCompanyFilter: string[] = [];

        if (filter?.companyIds?.length) {
            defaultCompanyFilter = filter.companyIds;
        } else if (companies.length) {
            defaultCompanyFilter = companies.map(company => company.id);
        } else if (company) {
            defaultCompanyFilter = [company.id];
        }

        return {
            ...filter,
            companyIds: defaultCompanyFilter,
            purpose: Purpose.Admin,
        };
    }, [companies, company, filter]);

    const { data, loading, error } = useListInsuranceProductsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: sort ? { field: sort.field, order: sort.order } : null,
            filter: wrappedFilter,
        },
    });

    const companyFilterList = useGetCompanyFilter({ collection: CompanyFilterListCollection.InsuranceProducts });

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const onChange = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            companyIds: !filters.company ? undefined : (filters.company as string[]),
                            displayName: !filters.displayName ? null : filters.displayName[0],
                            identifier: !filters.identifier ? null : filters.identifier[0],
                            insurerName: !filters.insurerName ? null : filters.insurerName[0],
                            legalName: !filters.legalName ? null : filters.legalName[0],
                            description: !filters.description ? null : filters.description[0],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // prepare items as a data source
    const [dataSource, total] = useMemo(() => {
        const items = (data?.list?.items || []).map(item => ({ ...item, key: item.id }));
        const itemCount = data?.list?.count || 0;

        return [items, itemCount];
    }, [data]);

    const {
        companySort,
        displayNameSort,
        identifierSort,
        insurerNameSort,
        orderSort,
        activeSort,
        startDateSort,
        endDateSort,
        typeSort,
    } = useSortOrder(sort);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (total === 0) {
            return <InsuranceProductEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: InsuranceProductListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/insuranceProducts/${row.id}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            scroll={{ x: true }}
            state={state}
            tableName={t('insuranceProductList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="company"
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, companyFilterList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter.companyIds}
                    sortOrder={companySort}
                    title={t('insuranceProductList:columns.company')}
                    sorter
                />
            )}
            <Table.Column
                key="identifier"
                dataIndex="identifier"
                sortOrder={identifierSort}
                title={t('insuranceProductList:columns.identifier')}
                sorter
            />
            <Table.Column
                key="displayName"
                dataIndex={['displayName']}
                filterDropdown={searchBox.render(t('insuranceProductList:columns.displayName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.displayName ? [filter.displayName] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={displayNameSort}
                title={t('insuranceProductList:columns.displayName')}
                sorter
            />
            <Table.Column
                key="type"
                dataIndex="type"
                render={value => value}
                sortOrder={typeSort}
                title={t('insuranceProductList:columns.type')}
                sorter
            />
            <Table.Column
                key="insurerName"
                dataIndex={['insurer', 'legalName', 'defaultValue']}
                filterDropdown={searchBox.render(t('insuranceProductList:columns.insurerName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter.insurerName ? [filter.insurerName] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                render={value => value}
                sortOrder={insurerNameSort}
                title={t('insuranceProductList:columns.insurerName')}
                sorter
            />
            <Table.Column
                key="start"
                dataIndex={['period', 'start']}
                render={(value, record: InsuranceProductListDataFragment) =>
                    t('common:formats.date', { date: new Date(value) })
                }
                sortOrder={startDateSort}
                title={t('insuranceProductList:columns.startDate')}
                sorter
            />
            <Table.Column
                key="end"
                dataIndex={['period', 'end']}
                render={(value, record: InsuranceProductListDataFragment) =>
                    t('common:formats.date', { date: new Date(value) })
                }
                sortOrder={endDateSort}
                title={t('insuranceProductList:columns.endDate')}
                sorter
            />
            <Table.Column
                key="order"
                align="center"
                dataIndex="order"
                sortOrder={orderSort}
                title={t('insuranceProductList:columns.order')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                sortOrder={activeSort}
                title={t('insuranceProductList:columns.isActive')}
                sorter
            />
        </PaginatedTableWithContext>
    );
};

export default InsuranceProductList;
