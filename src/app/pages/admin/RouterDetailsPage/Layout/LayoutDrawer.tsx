import { Button, Space } from 'antd';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { RouterSpecsFragment } from '../../../../api/fragments/RouterSpecs';
import DrawerWithAutoWidth from '../../../../components/DrawerWithAutoWidth';
import LayoutForm from './LayoutForm';

export type LayoutDrawerProps = {
    onClose: () => void;
    visible: boolean;
    router: RouterSpecsFragment;
};

const LayoutDrawer = ({ onClose, visible, router }: LayoutDrawerProps) => {
    const { t } = useTranslation(['routerDetails']);

    return (
        <DrawerWithAutoWidth
            extra={
                <Space>
                    <Button onClick={onClose}>{t('routerDetails:layouts.updateDrawer.cancelButton')}</Button>
                    <Button form="applyRouterLayoutForm" htmlType="submit" type="primary">
                        {t('routerDetails:layouts.updateDrawer.submitButton')}
                    </Button>
                </Space>
            }
            onClose={onClose}
            title={t('routerDetails:layouts.updateDrawer.title')}
            visible={visible}
            destroyOnClose
        >
            <LayoutForm closeDrawer={onClose} router={router} />
        </DrawerWithAutoWidth>
    );
};

export default LayoutDrawer;

export const useLayoutDrawer = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (router: LayoutDrawerProps['router']) => (
            <LayoutDrawer onClose={actions.close} router={router} visible={visible} />
        ),
    };
};
