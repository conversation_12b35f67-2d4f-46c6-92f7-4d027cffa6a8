import { useApolloClient } from '@apollo/client';
import { Button, Form, Space, message } from 'antd';
import { Formik } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { RouterSpecsFragment } from '../../../api/fragments/RouterSpecs';
import {
    UpdateRouterDocument,
    type UpdateRouterMutation,
    type UpdateRouterMutationVariables,
} from '../../../api/mutations/updateRouter';
import DrawerWithAutoWidth from '../../../components/DrawerWithAutoWidth';
import CheckboxField from '../../../components/fields/CheckboxField';
import InputField from '../../../components/fields/InputField';
import LanguageSelectField from '../../../components/fields/LanguageSelectField';
import useHandleError from '../../../utilities/useHandleError';

export type RouterUpdateDrawerProps = {
    router: RouterSpecsFragment;
    onClose: () => void;
    visible: boolean;
};

type FormValues = UpdateRouterMutationVariables['settings'];

const RouterUpdateDrawer = ({ router, onClose, visible }: RouterUpdateDrawerProps) => {
    const { t } = useTranslation(['routerDetails']);
    const apolloClient = useApolloClient();

    const routerId = router.id;

    const initialValues = useMemo(
        (): FormValues => ({
            hostname: router.hostname,
            pathname: router.pathname,
            withAdmin: router.withAdmin,
            languages: router.languages.map(language => language.id),
            googleTagManagerId: router.googleTagManagerId,
            pixelId: router.pixelId,
            linkedInInsightTagId: router.linkedInInsightTagId,
            blueKaiId: router.blueKaiId,
            tikTokId: router.tikTokId,
            snapChatId: router.snapChatId,
        }),
        [router]
    );

    const onSubmit = useHandleError(
        async (values: FormValues) => {
            message.loading({
                content: t('routerDetails:updateDrawer.messages.submitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient
                .mutate<UpdateRouterMutation, UpdateRouterMutationVariables>({
                    mutation: UpdateRouterDocument,
                    variables: { routerId, settings: values },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            message.success({
                content: t('routerDetails:updateDrawer.messages.success'),
                key: 'primary',
            });

            // then close the drawer
            onClose();
        },
        [onClose, t, apolloClient, routerId]
    );

    return (
        <DrawerWithAutoWidth
            extra={
                <Space>
                    <Button onClick={onClose}>{t('routerDetails:updateDrawer.cancelButton')}</Button>
                    <Button form="updateRouterSpecs" htmlType="submit" type="primary">
                        {t('routerDetails:updateDrawer.submitButton')}
                    </Button>
                </Space>
            }
            onClose={onClose}
            title={t('routerDetails:updateDrawer.title')}
            visible={visible}
            destroyOnClose
        >
            <Formik initialValues={initialValues} onSubmit={onSubmit}>
                {({ handleSubmit }) => (
                    <Form
                        id="updateRouterSpecs"
                        layout="vertical"
                        name="updateRouterSpecs"
                        onSubmitCapture={handleSubmit}
                    >
                        <InputField {...t('routerDetails:fields.hostname', { returnObjects: true })} name="hostname" />
                        <InputField {...t('routerDetails:fields.pathname', { returnObjects: true })} name="pathname" />
                        <CheckboxField
                            {...t('routerDetails:fields.withAdmin', { returnObjects: true })}
                            name="withAdmin"
                        />
                        <LanguageSelectField
                            {...t('routerDetails:fields.languages', { returnObjects: true })}
                            mode="multiple"
                            name="languages"
                        />
                        <InputField
                            {...t('routerDetails:fields.googleTagManagerId', { returnObjects: true })}
                            name="googleTagManagerId"
                        />
                        <InputField
                            {...t('routerDetails:fields.linkedInInsightTagId', { returnObjects: true })}
                            name="linkedInInsightTagId"
                        />
                        <InputField {...t('routerDetails:fields.pixelId', { returnObjects: true })} name="pixelId" />

                        <InputField {...t('routerDetails:fields.tikTokId', { returnObjects: true })} name="tikTokId" />
                        <InputField
                            {...t('routerDetails:fields.snapChatId', { returnObjects: true })}
                            name="snapChatId"
                        />
                        <InputField
                            {...t('routerDetails:fields.blueKaiId', { returnObjects: true })}
                            name="blueKaiId"
                        />
                    </Form>
                )}
            </Formik>
        </DrawerWithAutoWidth>
    );
};

export default RouterUpdateDrawer;

export const useUpdateDrawer = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (router: RouterUpdateDrawerProps['router']) => (
            <RouterUpdateDrawer onClose={actions.close} router={router} visible={visible} />
        ),
    };
};
