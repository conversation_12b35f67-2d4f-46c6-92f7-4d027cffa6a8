import { DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Space, message } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../shared/permissions';
import { useAccount, useAccountContext } from '../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import { exportInventories } from '../../../utilities/export';
import hasPermissions from '../../../utilities/hasPermissions';
import useGoTo from '../../../utilities/useGoTo';
import DeleteInventoryModal from './DeleteInventoryModal';
import { useImportInventoryModal } from './ImportInventoryModal';
import InventoryList from './InventoryList';

const InventoryListPage = () => {
    const { t } = useTranslation('inventoryList');
    const { token } = useAccountContext();
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const { dealerIds, dealerOptions } = useMultipleDealerIds();

    const importInventoryModal = useImportInventoryModal();

    const { permissions: accountPermissions } = useAccount();
    const company = useCompany(true);

    const goToNewInventory = useGoTo('/admin/inventories/add');

    const onClickDelete = () => {
        setShowDeleteModal(true);
    };

    const onClickImport = useCallback(() => {
        if (dealerIds.length !== 1) {
            message.warn(t('inventoryList:messages.selectDealerToImport'));

            return;
        }

        importInventoryModal.open();
    }, [dealerIds.length, importInventoryModal, t]);

    const onClickExport = useCallback(async () => {
        if (dealerIds.length !== 1) {
            message.warn(t('inventoryList:messages.selectDealerToExport'));

            return;
        }

        const [dealerId] = dealerIds;
        const dealerOption = dealerOptions.find(option => option.value === dealerId);
        await exportInventories(dealerId, dealerOption.label, token);
        message.success({
            content: t('inventoryList:messages.successExportInventories'),
            key: 'primary',
        });
    }, [dealerIds, dealerOptions, t, token]);

    const permissions = useMemo(() => {
        const [mayUpdateInventory, mayCreateInventory, mayDeleteInventory, mayViewInventories] = [
            hasPermissions(accountPermissions, [permissionKind.updateInventory]),
            hasPermissions(accountPermissions, [permissionKind.createInventory]),
            hasPermissions(accountPermissions, [permissionKind.deleteInventory]),
            hasPermissions(accountPermissions, [permissionKind.viewInventories]),
        ];

        if (!company) {
            return {
                manage: mayUpdateInventory,
                create: mayCreateInventory,
                delete: mayDeleteInventory,
                view: mayViewInventories,
            };
        }

        const { permissions: companyPermissions } = company;

        return {
            manage: hasPermissions(companyPermissions, [permissionKind.manageInventories]) && mayUpdateInventory,
            create: hasPermissions(companyPermissions, [permissionKind.createInventory]) && mayCreateInventory,
            delete: hasPermissions(companyPermissions, [permissionKind.deleteInventory]) && mayDeleteInventory,
            view: hasPermissions(companyPermissions, [permissionKind.viewInventories]) && mayViewInventories,
        };
    }, [accountPermissions, company]);

    const items: MenuProps['items'] = [
        permissions.create && {
            label: t('inventoryList:actions.add'),
            key: 'add',
            onClick: goToNewInventory,
        },
        ...(permissions.manage && [
            {
                key: 'import',
                onClick: onClickImport,
                label: t('inventoryList:actions.import'),
            },
            {
                key: 'export',
                onClick: onClickExport,
                label: t('inventoryList:actions.export'),
            },
        ]),
        permissions.delete && {
            danger: true,
            key: 'delete',
            onClick: onClickDelete,
            label: t('inventoryList:actions.delete'),
        },
    ].filter(Boolean);

    return (
        <ConsolePageWithHeader
            extra={
                items.length > 0 && (
                    <Dropdown menu={{ items }}>
                        <Button type="primary">
                            <Space>
                                <span>{t('inventoryList:actions.actions')}</span>
                                <DownOutlined />
                            </Space>
                        </Button>
                    </Dropdown>
                )
            }
            title={t('inventoryList:title')}
        >
            <DeleteInventoryModal isVisible={showDeleteModal} onFinish={() => setShowDeleteModal(false)} />
            <InventoryList />
            {importInventoryModal.render()}
        </ConsolePageWithHeader>
    );
};

export default InventoryListPage;
