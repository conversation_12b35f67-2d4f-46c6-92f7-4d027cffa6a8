import { message } from 'antd';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDeleteInventoriesMutation } from '../../../api/mutations/deleteInventories';
import { ListInventoriesDocument } from '../../../api/queries/listInventories';
import Form from '../../../components/fields/Form';
import { useThemeComponents } from '../../../themes/hooks';
import useHandleError from '../../../utilities/useHandleError';
import { SelectionSectionFields } from '../AddInventoryPage/SelectionSection';
import useInventoryValidator from '../AddInventoryPage/useInventoryValidator';
import type { FormValues } from '../AddInventoryPage/utils';
import { defaultValue } from '../AddInventoryPage/utils';

type DeleteInventoryModalProps = {
    isVisible: boolean;
    onFinish: () => void;
};
const DeleteInventoryModal = ({ isVisible, onFinish }: DeleteInventoryModalProps) => {
    const { t } = useTranslation(['inventoryList', 'inventoryDetails']);
    const [requireDealer, setRequireDealer] = useState(true);
    const { Modal } = useThemeComponents();

    const [deleteInventories] = useDeleteInventoriesMutation({
        refetchQueries: [ListInventoriesDocument],
    });

    const validate = useInventoryValidator(requireDealer);

    const onSubmit = useHandleError<FormValues>(
        async values => {
            const settings = omit(['modelId', 'subModelId', 'variantId'], values);

            message.loading({
                content: t('inventoryDetails:messages.deleteInventories.deleteSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await deleteInventories({
                variables: {
                    settings,
                },
            }).finally(() => {
                message.destroy('primary');
            });

            message.success({
                content: t('inventoryDetails:messages.deleteInventories.deleteSuccessful'),
                key: 'primary',
            });
            onFinish();
        },
        [deleteInventories, onFinish, t]
    );

    return (
        <Modal
            okButtonProps={{
                form: 'deleteInventory',
                htmlType: 'submit',
            }}
            onCancel={onFinish}
            title={t('inventoryList:modals.delete.title')}
            visible={isVisible}
            width={600}
            centered
        >
            <Formik initialValues={defaultValue} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="deleteInventory" name="deleteInventory" onSubmitCapture={handleSubmit}>
                        <SelectionSectionFields optionsMode="delete" setRequireDealer={setRequireDealer} />
                    </Form>
                )}
            </Formik>
        </Modal>
    );
};

export default DeleteInventoryModal;
