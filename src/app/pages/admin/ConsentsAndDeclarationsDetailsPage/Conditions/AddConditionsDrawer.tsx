import { Space, Button, message } from 'antd';
import { isEmpty } from 'lodash/fp';
import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { ModuleSpecsFragment } from '../../../../api/fragments/ModuleSpecs';
import DrawerWithAutoWidth from '../../../../components/DrawerWithAutoWidth';
import AddConditionForm from './AddConditionForm';

import { isConditionSettingsComplete } from './shared';

export type AddConditionsDrawerProps = {
    name: string;
    index: number;
    value;
    onAdd: () => void;
    onClose: () => void;
    visible: boolean;
    module?: ModuleSpecsFragment;
};

const AddConditionsDrawer = ({ name, index, value, onAdd, onClose, visible, module }: AddConditionsDrawerProps) => {
    const { t } = useTranslation(['consentsAndDeclarations']);

    return (
        <DrawerWithAutoWidth
            extra={
                <Space>
                    <Button onClick={onClose}>{t('consentsAndDeclarations:conditionsDrawer.cancelButton')}</Button>
                    <Button onClick={onAdd} type="primary">
                        {t('consentsAndDeclarations:conditionsDrawer.submitButton')}
                    </Button>
                </Space>
            }
            onClose={onClose}
            open={visible}
            title={t('consentsAndDeclarations:conditionsDrawer.title')}
            destroyOnClose
        >
            <AddConditionForm index={index} module={module} name={name} value={value} />
        </DrawerWithAutoWidth>
    );
};

export default AddConditionsDrawer;

export const useAddConditionsDrawer = (name: string, index: number, value, module?: ModuleSpecsFragment) => {
    const { t } = useTranslation(['consentsAndDeclarations']);

    const [visible, setVisible] = useState(false);
    const [isNewCondition, setIsNewCondition] = useState(false);
    const actions = useMemo(
        () => ({
            open: (newCondition: boolean) => {
                setIsNewCondition(newCondition);
                setVisible(true);
            },
            close: (onDelete: () => void) => {
                if (!isNewCondition && !isConditionSettingsComplete(value[index])) {
                    // existing condition but is incomplete
                    message.warn({
                        content: t('consentsAndDeclarations:conditionsDrawer.messages.incompleteCondition'),
                        key: 'primary',
                    });
                } else if (isNewCondition || value[index] === '' || isEmpty(value[index])) {
                    // only remove value if it's empty or it's a new condition
                    onDelete();
                    setVisible(false);
                } else {
                    setVisible(false);
                }
            },
            add: () => {
                setVisible(false);
                message.success({
                    content: t('consentsAndDeclarations:conditionsDrawer.messages.success'),
                    key: 'primary',
                });
            },
        }),
        [index, isNewCondition, t, value]
    );

    return {
        ...actions,
        render: (onDelete: () => void) => (
            <AddConditionsDrawer
                index={index}
                module={module}
                name={name}
                onAdd={actions.add}
                onClose={() => actions.close(onDelete)}
                value={value}
                visible={visible}
            />
        ),
    };
};
