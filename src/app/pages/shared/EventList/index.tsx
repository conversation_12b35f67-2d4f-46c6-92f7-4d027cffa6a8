import { Table } from 'antd';
import type { ColumnFilterItem, FilterDropdownProps } from 'antd/es/table/interface';
import { pick } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router';
import urlJoin from 'url-join';
import type { EventListDataFragment } from '../../../api/fragments/EventListData';
import { useListEventFiltersQuery } from '../../../api/queries/listEventFilters';
import { useListEventsQuery } from '../../../api/queries/listEvents';
import type { Event } from '../../../api/types';
import { CompanyFilterListCollection } from '../../../api/types';
import FilterBox from '../../../components/FilterBox';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import RangeDateFilterBox, { objectToRangeDateValue } from '../../../components/RangeDateFilterBox';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import useSearchInDropDown from '../../../components/useSearchInDropDown';
import { checkHasFilter } from '../../../utilities/common';
import { getDateTimeOffsetFormat } from '../../../utilities/date';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderFilterIcon from '../../../utilities/renderFilterIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useAccessFilter from '../../../utilities/useAccessFilter';
import useActiveFilter from '../../../utilities/useActiveFilter';
import useEnableFilter from '../../../utilities/useEnableFilter';
import useGetCompanyFilter from '../../../utilities/useGetCompanyFilter';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import EventEmptyListResult from './EventEmptyListResult';
import RenderShareUrl from './RenderShareUrl';
import type { PageType } from './RenderShareUrl';
import useListReducer from './useEventReducer';
import useSortOrder, { getSortField } from './useSortOrder';

export enum EventColumn {
    EventId = 'Event ID',
    EventName = 'Event Name',
    StartDate = 'Start Date',
    EndDate = 'End Date',
    Assigned = 'Assigned',
    Payment = 'Payment',
    Access = 'Access',
    Active = 'Active',
    URL = 'URL',
    Company = 'Company',
    ModuleName = 'ModuleName',
}
export type EventListProps = {
    pageType: PageType;
    moduleId?: string;
    dealerIds?: string[];
};

const EventList = ({ pageType, moduleId, dealerIds }: EventListProps) => {
    const { t } = useTranslation(['eventList', 'common']);

    const navigate = useNavigate();

    // get company context
    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Event);

    // get state from a reducer
    const [state, dispatch] = useListReducer(currentCache);

    // fetch data
    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListEventsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: {
                companyId: pageType === 'Admin' ? company?.id : undefined,
                moduleId: pageType === 'Portal' ? moduleId : undefined,
                productionOnly: false,
                dealerIds,
                ...filter,
            },
        },
    });
    // prepare items as data source
    const dataSource = useMemo(() => (data?.lists?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const { data: eventFilterData } = useListEventFiltersQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            companyId: pageType === 'Admin' ? company?.id : undefined,
            moduleId: pageType === 'Portal' ? moduleId : undefined,
            productionOnly: false,
            dealerIds,
        },
    });

    const uniqueModules = eventFilterData?.list?.moduleCount;

    const total = data?.lists?.count || 0;

    const onChange = useCallback(
        (pagination, filter, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            assigned: !filter[EventColumn.Assigned] ? null : filter[EventColumn.Assigned][0],
                            displayName: !filter[EventColumn.EventName] ? null : filter[EventColumn.EventName][0],
                            privateAccesses: !filter[EventColumn.Access] ? [] : filter[EventColumn.Access],
                            payment: !filter[EventColumn.Payment] ? [] : filter[EventColumn.Payment],
                            moduleName: !filter[EventColumn.ModuleName] ? null : filter[EventColumn.ModuleName][0],
                            startDate: !filter[EventColumn.StartDate]
                                ? null
                                : {
                                      start: new Date(filter[EventColumn.StartDate][0] as string),
                                      end: new Date(filter[EventColumn.StartDate][1] as string),
                                  },
                            endDate: !filter[EventColumn.EndDate]
                                ? null
                                : {
                                      start: new Date(filter[EventColumn.EndDate][0] as string),
                                      end: new Date(filter[EventColumn.EndDate][1] as string),
                                  },
                            companyIds: !filter[EventColumn.Company] ? [] : filter[EventColumn.Company],
                            urlEnabled: !filter[EventColumn.URL] ? [] : filter[EventColumn.URL],
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    // splice share link
    const location = useLocation();
    const eventJourneyLink = useCallback(
        e => {
            if (!e.isActive) {
                return '';
            }

            const routerPath = pageType === 'Portal' ? location.pathname : e.firstRouterPath;

            return urlJoin(routerPath, `/${e.urlSlug}`);
        },
        [location.pathname, pageType]
    );

    const searchBox = useSearchInDropDown();
    const showRangeFilterDropDown = useCallback(
        (props: FilterDropdownProps) =>
            RangeDateFilterBox({
                filterDropDownProps: props,
                picker: 'date',
            }),
        []
    );

    const showFilterDropDown = useCallback(
        (props: FilterDropdownProps, filters: ColumnFilterItem[]) =>
            FilterBox({
                filterDropDownProps: props,
                filters,
            }),
        []
    );

    const enableFilter = useEnableFilter();
    const accessFilter = useAccessFilter();
    const companyFilterList = useGetCompanyFilter({ collection: CompanyFilterListCollection.Events });

    const { companySort, eventIdSort, nameSort, assignedSort, paymentSort, moduleNameSort } = useSortOrder(sort);

    const getListLink = e =>
        pageType === 'Portal' ? urlJoin(location.pathname, 'details', e.urlSlug) : urlJoin(location.pathname, e.id);

    const hasFilter = useMemo(() => checkHasFilter(filter), [filter]);

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (dataSource.length === 0 && pageType === 'Admin' && !hasFilter) {
            return <EventEmptyListResult />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: EventListDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(getListLink(row));
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            scroll={{ x: true }}
            state={state}
            tableName={t('eventList:title')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key={EventColumn.Company}
                    dataIndex={['module', 'company', 'displayName']}
                    filterDropdown={props => showFilterDropDown(props, companyFilterList)}
                    filterIcon={renderFilterIcon}
                    filteredValue={filter?.companyIds}
                    sortOrder={companySort}
                    title={t('eventList:columns.company')}
                    sorter
                />
            )}
            {uniqueModules > 1 && (
                <Table.Column
                    key={EventColumn.ModuleName}
                    dataIndex={['module', 'displayName']}
                    filterDropdown={searchBox.render(t('eventList:columns.moduleName'))}
                    filterIcon={renderSearchIcon}
                    filteredValue={filter?.moduleName ? [filter.moduleName] : undefined}
                    onFilterDropdownVisibleChange={searchBox.autoFocus}
                    sortOrder={moduleNameSort}
                    title={t('eventList:columns.moduleName')}
                    sorter
                />
            )}
            <Table.Column
                key={EventColumn.EventId}
                dataIndex="identifier"
                sortOrder={eventIdSort}
                title={t('eventList:columns.eventId')}
                sorter
            />
            <Table.Column
                key={EventColumn.EventName}
                dataIndex="displayName"
                filterDropdown={searchBox.render(t('eventList:columns.eventName'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter?.displayName ? [filter.displayName] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={nameSort}
                title={t('eventList:columns.eventName')}
                sorter
            />
            <Table.Column
                key={EventColumn.StartDate}
                dataIndex={['period', 'start']}
                filterDropdown={props => showRangeFilterDropDown(props)}
                filterIcon={renderFilterIcon}
                filteredValue={objectToRangeDateValue(filter?.startDate)}
                render={(value, event: Event) => getDateTimeOffsetFormat(value, t, event.module.company.timeZone)}
                title={t('eventList:columns.startDate')}
            />
            <Table.Column
                key={EventColumn.EndDate}
                dataIndex={['period', 'end']}
                filterDropdown={props => showRangeFilterDropDown(props)}
                filterIcon={renderFilterIcon}
                filteredValue={objectToRangeDateValue(filter?.endDate)}
                render={(value, event: Event) => getDateTimeOffsetFormat(value, t, event.module.company.timeZone)}
                title={t('eventList:columns.endDate')}
            />
            <Table.Column
                key={EventColumn.Assigned}
                dataIndex={['publicSalesPerson', 'defaultSalesPerson', 'displayName']}
                filterDropdown={searchBox.render(t('eventList:columns.assigned'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter?.assigned ? [filter.assigned] : undefined}
                onFilterDropdownVisibleChange={searchBox.autoFocus}
                sortOrder={assignedSort}
                title={t('eventList:columns.assigned')}
                sorter
            />
            <Table.Column
                key={EventColumn.Payment}
                align="center"
                dataIndex="hasPayment"
                filterDropdown={props => showFilterDropDown(props, enableFilter)}
                filterIcon={renderFilterIcon}
                filteredValue={filter?.payment}
                render={value => renderBooleanIcon(value, true)}
                sortOrder={paymentSort}
                title={t('eventList:columns.payment')}
                sorter
            />
            <Table.Column
                key={EventColumn.Access}
                align="center"
                dataIndex="privateAccess"
                filterDropdown={props => showFilterDropDown(props, accessFilter)}
                filterIcon={renderFilterIcon}
                filteredValue={filter?.privateAccesses}
                render={value => (value ? t('eventList:data.login') : t('eventList:data.public'))}
                title={t('eventList:columns.access')}
            />
            <Table.Column
                key={EventColumn.URL}
                align="center"
                dataIndex="firstRouterPath"
                filterDropdown={props => showFilterDropDown(props, enableFilter)}
                filterIcon={renderFilterIcon}
                filteredValue={filter.urlEnabled}
                render={(value, event: Event) => (
                    <RenderShareUrl event={event} pageType={pageType} shareIconLink={eventJourneyLink(event)} />
                )}
                title={t('eventList:columns.url')}
            />
        </PaginatedTableWithContext>
    );
};

export default EventList;
