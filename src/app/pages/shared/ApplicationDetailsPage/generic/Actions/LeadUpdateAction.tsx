import { useApolloClient } from '@apollo/client';
import { message, Modal } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
    UpdateLeadDocument,
    type UpdateLeadMutation,
    type UpdateLeadMutationVariables,
} from '../../../../../api/mutations/updateLead';
import { prepareKYCFieldPayload } from '../../../../../utilities/kycPresets';
import type { LeadFormApplication, LeadFormValues } from '../../../../admin/LeadDetailsPage/shared';
import { useNavigateWithBlock } from '../../../blockBackRedirection';
import { hasCustomerUpdate, useDialCode } from '../../standard/useUpdates';
import ActionButton from '../ActionButton';

type LeadUpdateActionProps = {
    lead: LeadFormApplication;
    forCI?: boolean;
    disabled?: boolean;
};

const LeadUpdateAction = ({ lead, forCI, disabled }: LeadUpdateActionProps) => {
    const { t } = useTranslation('applicationDetails');
    const navigate = useNavigateWithBlock();
    const apolloClient = useApolloClient();
    const dialCode = useDialCode();

    const { values, validateForm, initialValues } = useFormikContext<LeadFormValues>();

    const onUpdate = useCallback(async () => {
        await validateForm();

        const customer = prepareKYCFieldPayload(values.customer.fields);

        const isNeedUpdate = hasCustomerUpdate(customer, values.customer.fields, initialValues?.customer.fields, {
            dialCode,
        });
        if (!isNeedUpdate) {
            message.warn(
                t('applicationDetails:messages.noUpdated', { action: t('applicationDetails:buttons.update') })
            );

            return;
        }

        Modal.confirm({
            className: 'static-modal',
            title: t(`applicationDetails:confirmModal.title.${lead.isLead ? 'lead' : 'contact'}`),
            okText: t('applicationDetails:buttons.ok'),
            cancelText: t('applicationDetails:buttons.cancel'),
            onOk: async () => {
                try {
                    message.loading({
                        content: t('applicationDetails:messages.submittingChanges'),
                        key: 'primary',
                        duration: 0,
                    });
                    await apolloClient.mutate<UpdateLeadMutation, UpdateLeadMutationVariables>({
                        mutation: UpdateLeadDocument,
                        variables: {
                            leadId: lead.id,
                            input: {
                                customer,
                            },
                        },
                    });

                    // inform about success
                    message.success({
                        content: t('applicationDetails:messages.submittedChanges'),
                        key: 'primary',
                    });

                    if (forCI) {
                        navigate(-1);
                    }
                } catch (error) {
                    console.error(error);
                    message.destroy('primary');
                }
            },
        });
    }, [apolloClient, lead, initialValues, navigate, t, validateForm, values, dialCode, forCI]);

    return (
        <ActionButton disabled={disabled} forCI={forCI} onClick={onUpdate} size="large" type="primary">
            {t('applicationDetails:buttons.update')}
        </ActionButton>
    );
};

export default LeadUpdateAction;
