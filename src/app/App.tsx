import { PorscheDesignSystemProvider, PToast } from '@porsche-design-system/components-react';
import { ConfigProvider } from 'antd';
import type { DirectionType } from 'antd/es/config-provider';
import type { i18n as I18n } from 'i18next';
import { Suspense, useMemo } from 'react';
import { I18nextProvider, useTranslation } from 'react-i18next';
import { ThemeProvider } from 'styled-components';
import type { BootstrapProps } from './Bootstrap';
import Bootstrap from './Bootstrap';
import LoadingElement from './components/LoadingElement';
import SystemMessageHandler from './components/SystemMessageHandler';
import AccountContextManager from './components/contexts/AccountContextManager';
import { useAntdTheme } from './components/contexts/AntdThemeProvider';
import RouterContextManager from './components/contexts/RouterContextManager';
import ThemeController from './components/contexts/ThemeController';
import HeaderContextManager from './layouts/HeaderContextManager';
import MainRouter from './routers/MainRouter';
import type { RuntimeConfig } from './runtimeConfig';
import { RuntimeProvider, useRuntimeConfig } from './runtimeConfig';
import theme from './theme';
import ThemeComponentsProvider from './themes';
import useDateTranslation from './utilities/useDateTranslation';
import useObserverRoot from './utilities/useObserveRoot';

export type AppProps = {
    i18n: I18n;
    runtime: RuntimeConfig;
    createApolloClient: BootstrapProps['createApolloClient'];
};

const AppThemeProvider = ({ children }: { children: JSX.Element | React.ReactNode }) => {
    const { i18n } = useTranslation(['core', 'antd']);
    const locale = i18n.getResourceBundle(i18n.language, 'antd');
    const {
        router: { layoutType },
    } = useRuntimeConfig();

    useObserverRoot();

    useDateTranslation(locale.locale as string);

    const themeWithLayout = useMemo(
        () => ({
            ...theme,
            layoutType,
        }),
        [layoutType]
    );

    return (
        <ThemeProvider theme={themeWithLayout}>
            <ThemeController>
                <AntdConfigProvider>
                    <PorscheDesignSystemProvider>
                        <PToast />
                        <ThemeComponentsProvider>{children}</ThemeComponentsProvider>
                    </PorscheDesignSystemProvider>
                </AntdConfigProvider>
            </ThemeController>
        </ThemeProvider>
    );
};

const AntdConfigProvider = ({ children }: { children: JSX.Element | React.ReactNode }) => {
    const { t, i18n } = useTranslation(['core', 'antd']);
    const locale = i18n.getResourceBundle(i18n.language, 'antd');
    const { theme: antdTheme } = useAntdTheme();

    return (
        <ConfigProvider direction={t('core:orientation') as DirectionType} locale={locale} theme={antdTheme}>
            {children}
        </ConfigProvider>
    );
};

const Inner = () => (
    <Suspense fallback={<LoadingElement />}>
        <AccountContextManager>
            <RouterContextManager>
                <HeaderContextManager>
                    <SystemMessageHandler />
                    <MainRouter />
                </HeaderContextManager>
            </RouterContextManager>
        </AccountContextManager>
    </Suspense>
);

const App = ({ i18n, runtime, createApolloClient }: AppProps) => (
    <RuntimeProvider runtime={runtime}>
        <I18nextProvider i18n={i18n}>
            <AppThemeProvider>
                <Bootstrap createApolloClient={createApolloClient}>
                    <Inner />
                </Bootstrap>
            </AppThemeProvider>
        </I18nextProvider>
    </RuntimeProvider>
);

export default App;
