import type { Dispatch } from 'react';
import { createContext, useContext, useEffect, useId, useMemo, useReducer, useRef } from 'react';
import { createGlobalStyle, css } from 'styled-components';
import { CompanyTheme } from '../../api/types';
import {
    DEFAULT_THEME_CLASSNAME,
    PORSCHE_THEME_CLASSNAME,
    PORSCHE_V3_THEME_CLASSNAME,
    SKODA_THEME_CLASSNAME,
    VW_THEME_CLASSNAME,
} from '../../themes/shared';
import { AntdThemeProvider } from './AntdThemeProvider';

export type ThemeOverride = {
    // custom font
    font?: string;

    // custom font bold
    fontBold?: string;

    // UID
    id: string;

    // primary color
    primaryColor: string;

    theme: CompanyTheme;
};

const getFontType = (fontUrl: string) => {
    const extension = fontUrl.toLowerCase().split('.').slice(-1)[0];

    switch (extension) {
        case 'woff':
        case 'woff2':
            return extension;

        case 'ttf':
            return 'truetype';

        case 'otf':
            return 'opentype';

        default:
            throw new Error(`Unsupported format ${extension}`);
    }
};

const getThemeClassName = (theme: CompanyTheme) => {
    switch (theme) {
        case CompanyTheme.Default:
            return DEFAULT_THEME_CLASSNAME;

        case CompanyTheme.Porsche:
            return PORSCHE_THEME_CLASSNAME;

        case CompanyTheme.Volkswagen:
            return VW_THEME_CLASSNAME;

        case CompanyTheme.Skoda:
            return SKODA_THEME_CLASSNAME;

        case CompanyTheme.PorscheV3:
            return PORSCHE_V3_THEME_CLASSNAME;

        default:
            return `theme-${theme.toLowerCase()}`;
    }
};

const GlobalStyle = createGlobalStyle<{ currentTheme: ThemeOverride }>`
    :root {
        --mobility-page-max-width: 120rem;
    }

    ${props =>
        (props.currentTheme.font || props.currentTheme.fontBold) &&
        css`
            ${props.currentTheme.font &&
            `
                @font-face {
                    font-family: 'CustomerFont-${props.currentTheme.id}';
                    src: url('${props.currentTheme.font}') format('${getFontType(props.currentTheme.font)}');
                    font-weight: normal;

                    // this make first text render a little faster
                    // https://web.dev/font-best-practices/
                    font-display: swap;
                }
            `}

            ${props.currentTheme.fontBold &&
            `
                @font-face {
                    font-family: 'CustomerFont-${props.currentTheme.id}';
                    src: url('${props.currentTheme.fontBold}') format('${getFontType(props.currentTheme.fontBold)}');
                    font-weight: bold;

                    // this make first text render a little faster
                    // https://web.dev/font-best-practices/
                    font-display: swap;
                }
            `}
        `}

    ${props =>
        props.currentTheme.theme !== CompanyTheme.Admin &&
        props.currentTheme.id !== 'none' &&
        css`
            html,
            body {
                font-family: 'CustomerFont-${props.currentTheme.id}', var(--font-family) !important;
            }
        `}
        
     ${props =>
         props.currentTheme.theme !== CompanyTheme.Admin &&
         props.currentTheme.id === 'none' &&
         css`
             html,
             body {
                 font-family: var(--font-family) !important;
             }
         `}
    
    ${props =>
        props.currentTheme.theme === CompanyTheme.Admin &&
        css`
            html,
            body {
                font-family: var(--font-family) !important;
            }
        `}
 `;

export type ThemeControllerProps = { children: JSX.Element };

type State = ThemeOverride[];

type Action =
    | { type: 'register'; override: ThemeOverride }
    | { type: 'update'; override: ThemeOverride }
    | { type: 'unregister'; id: string };

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'register':
            return [...state, action.override];

        case 'unregister':
            return state.filter(override => override.id === action.id);

        case 'update': {
            const index = state.findIndex(override => override.id === action.override.id);

            if (index >= 0) {
                // clone the state
                const newState = [...state];
                // replace the element
                newState[index] = action.override;

                return newState;
            }

            return state;
        }

        default:
            return state;
    }
};

const ThemeController = ({ children }: ThemeControllerProps) => {
    const [overrides, dispatch] = useReducer(reducer, []);

    const currentTheme = useMemo(
        () =>
            overrides.reduce<ThemeOverride>(
                (acc, override) => ({
                    ...acc,
                    ...override,
                }),
                {
                    font: null,
                    fontBold: null,
                    id: 'none',
                    primaryColor: '#1890ff',
                    theme: CompanyTheme.Default,
                }
            ),
        [overrides]
    );

    useEffect(() => {
        // remove theme specific class names used in global styles
        Object.values(CompanyTheme)
            .filter(value => currentTheme.theme !== value)
            .map(getThemeClassName)
            .map(value => document.documentElement.classList.remove(value));

        document.documentElement.classList.add(getThemeClassName(currentTheme.theme));

        return () => {
            document.documentElement.classList.remove(getThemeClassName(currentTheme.theme));
        };
    }, [currentTheme]);

    return (
        <ThemeControllerContext.Provider value={dispatch}>
            <AntdThemeProvider currentTheme={currentTheme}>
                <GlobalStyle currentTheme={currentTheme} />
                {children}
            </AntdThemeProvider>
        </ThemeControllerContext.Provider>
    );
};

export default ThemeController;

const ThemeControllerContext = createContext<Dispatch<Action> | null>(null);

export const useThemeOverride = (override: Omit<ThemeOverride, 'id'>) => {
    const id = useId();
    const dispatch = useContext(ThemeControllerContext);
    const registeredRef = useRef(false);

    if (!dispatch) {
        throw new Error('Theme controller context is missing');
    }

    // register or update whenever the override changes
    useEffect(() => {
        // sent the override
        dispatch({
            type: registeredRef.current ? 'update' : 'register',
            override: { id, ...override },
        });

        // update reference to update next time
        registeredRef.current = true;
    }, [dispatch, id, override, registeredRef]);

    // unregister when unmounting
    useEffect(() => () => dispatch({ type: 'unregister', id }), [dispatch, id]);
};
