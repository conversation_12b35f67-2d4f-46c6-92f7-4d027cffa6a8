import type { ThemeConfig } from 'antd';
import { createContext, useContext, useMemo, type ReactNode } from 'react';
import { CompanyTheme } from '../../api/types';
import { createAntdV5Theme, getThemeTokensForBrand } from '../../themes/antdV5ThemeUtils';
import type { ThemeOverride } from './ThemeController';

interface AntdThemeContextType {
    theme: ThemeConfig;
}

const AntdThemeContext = createContext<AntdThemeContextType | null>(null);

export const useAntdTheme = (): AntdThemeContextType => {
    const context = useContext(AntdThemeContext);
    if (!context) {
        throw new Error('useAntdTheme must be used within AntdThemeProvider');
    }

    return context;
};

interface AntdThemeProviderProps {
    children: ReactNode;
    currentTheme: ThemeOverride;
}

export const AntdThemeProvider = ({ children, currentTheme }: AntdThemeProviderProps) => {
    const theme = useMemo(() => {
        // Get base theme tokens for the current brand
        const tokens = getThemeTokensForBrand(currentTheme.theme, currentTheme.primaryColor);

        // Create Ant Design v5 theme configuration
        return createAntdV5Theme(tokens);
    }, [currentTheme.theme, currentTheme.primaryColor]);

    const value = useMemo(() => ({ theme }), [theme]);

    return <AntdThemeContext.Provider value={value}>{children}</AntdThemeContext.Provider>;
};
