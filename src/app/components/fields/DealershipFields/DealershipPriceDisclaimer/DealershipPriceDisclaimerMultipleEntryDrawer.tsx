import { PlusOutlined } from '@ant-design/icons';
import type { DrawerProps } from 'antd';
import { Button, Space, Spin, Form, Result } from 'antd';
import { useField } from 'formik';
import type { TFunction } from 'i18next';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetDealersOptionsQuery } from '../../../../api/queries/getDealersOptions';
import type { ListDealerOption } from '../../../../api/types';
import DrawerWithAutoWidth from '../../../DrawerWithAutoWidth';
import type { DisclaimerKind } from '../../DisclaimersDrawerField/types';
import { useTranslationDrawer } from '../../TranslatedInputField/TranslationDrawer';
import TranslatedInputFieldArray from '../../TranslatedInputFieldArray';
import { useAddDealershipModal } from '../AddDealershipModal';
import { useAddDealershipTextModal } from '../AddDealershipTextModal';
import type { DealershipItem } from '../DealershipSelectField/DealershipDrawer';
import type { DealershipTranslationKey } from '../shared';

export type DealershipPriceDisclaimerMultipleEntryDrawerProps = Pick<DrawerProps, 'title' | 'visible' | 'onClose'> & {
    name: string;
    dealers?: ListDealerOption[];
    disclaimerKind: DisclaimerKind;
    isEdit?: boolean;
    renderField?: (item: DealershipItem, onDelete: () => void, onOpenTranslation: () => void) => JSX.Element;
};

const getTranslation = (t: TFunction, disclaimerKind: DisclaimerKind) =>
    t(`dealershipField:${disclaimerKind}`, { returnObjects: true }) as DealershipTranslationKey;

const DealershipPriceDisclaimerMultipleEntryDrawer = ({
    onClose,
    title,
    visible,
    name,
    dealers: dealersFromProps,
    disclaimerKind,
    renderField = renderInputField,
    isEdit,
}: DealershipPriceDisclaimerMultipleEntryDrawerProps) => {
    const { t } = useTranslation(['dealershipField', 'translationField']);

    const translationObject = getTranslation(t, `${disclaimerKind}`);

    // For default value
    const [{ value: defaultPriceDisclaimerValue }, , { setValue: setDefaultValue }] = useField<
        { defaultValue: string; overrides: [] }[]
    >({ name: `${name}.defaultValue` });

    // For dealership specific value
    const [{ value: dealerships }, , { setValue }] = useField<{ dealerId: string; value: object[] }[]>({
        name: `${name}.overrides`,
    });

    const { data, loading } = useGetDealersOptionsQuery({
        fetchPolicy: 'cache-and-network',
        skip: !!dealersFromProps,
    });

    const translatedDrawer = useTranslationDrawer();

    const dealersFromQuery = data?.dealers;

    const allDealers = useMemo(() => dealersFromProps || dealersFromQuery || [], [dealersFromProps, dealersFromQuery]);
    const remainingDealers = useMemo(() => {
        if (!dealerships) {
            return allDealers;
        }

        return allDealers.filter(dealer => !dealerships.some(dealership => dealership.dealerId === dealer.id));
    }, [allDealers, dealerships]);

    const currentDealerships = useMemo(() => {
        if (!dealerships) {
            return allDealers;
        }

        return allDealers.filter(dealer => dealerships.some(dealership => dealership.dealerId === dealer.id));
    }, [allDealers, dealerships]);

    const actions = useMemo(() => {
        const currentDealers = dealerships || [];

        return {
            add: (dealerId: string) =>
                setValue([...currentDealers, { dealerId, value: [{ defaultValue: '', overrides: [] }] }]),
            addDisclaimer: (dealerId: string) => {
                if (dealerId === '0') {
                    setDefaultValue([...defaultPriceDisclaimerValue, { defaultValue: '', overrides: [] }]);
                } else {
                    const newDealer = currentDealers.map(dealer => {
                        if (dealer.dealerId === dealerId) {
                            return {
                                ...dealer,
                                value: [...dealer.value, { defaultValue: '', overrides: [] }],
                            };
                        }

                        return dealer;
                    });
                    setValue(newDealer);
                }
            },
            delete: (index: number) => {
                const newValue = [...currentDealers];
                newValue.splice(index, 1);
                setValue(newValue);
            },
        };
    }, [setValue, dealerships, setDefaultValue, defaultPriceDisclaimerValue]);

    const addModal = useAddDealershipModal(remainingDealers, actions.add);
    const addDisclaimerModel = useAddDealershipTextModal(currentDealerships, actions.addDisclaimer, translationObject);

    const cleanedDealerships = useMemo((): DealershipItem[] => {
        if (!dealerships) {
            return [];
        }

        return dealerships
            .map((dealership, index) => {
                const dealer = allDealers.find(dealer => dealer.id === dealership.dealerId);

                return { ...dealership, dealer, name: `${name}.overrides[${index}].value`, index };
            })
            .filter(dealership => !!dealership.dealer)
            .sort((a, b) => a.dealer.displayName.localeCompare(b.dealer.displayName));
    }, [dealerships, allDealers, name]);

    const content = (
        <Space direction="vertical" style={{ width: '100%' }}>
            {cleanedDealerships.length > 0 && (
                <Form layout="vertical">
                    {cleanedDealerships.map(dealership =>
                        renderField(dealership, () => actions.delete(dealership.index), translatedDrawer.open)
                    )}
                </Form>
            )}
            {!isEdit && (
                <>
                    <Button icon={<PlusOutlined />} onClick={addDisclaimerModel.open} type="dashed" block>
                        {translationObject.addButton}
                    </Button>
                    {remainingDealers.length > 0 && (
                        <Button icon={<PlusOutlined />} onClick={addModal.open} type="dashed" block>
                            {t('dealershipField:default.addDealer')}
                        </Button>
                    )}
                    {addDisclaimerModel.render()}
                    {addModal.render()}
                    {!cleanedDealerships.length && !remainingDealers.length && (
                        <Result status="info" subTitle="No dealer registered in the system yet" />
                    )}
                </>
            )}
        </Space>
    );

    return (
        <DrawerWithAutoWidth
            maxWidth={40}
            onClose={onClose}
            placement="right"
            title={title}
            visible={visible}
            destroyOnClose
        >
            <Spin spinning={loading}>
                <Form layout="vertical">
                    <TranslatedInputFieldArray
                        label={t('translationField:defaultValue')}
                        name={`${name}.defaultValue`}
                        isDefault
                    />
                </Form>

                {content}
            </Spin>
        </DrawerWithAutoWidth>
    );
};

export const renderInputField: DealershipPriceDisclaimerMultipleEntryDrawerProps['renderField'] = (
    market,
    onDelete
) => (
    <TranslatedInputFieldArray
        key={market.dealerId}
        label={market.dealer.displayName}
        name={market.name}
        onDelete={onDelete}
    />
);

export default DealershipPriceDisclaimerMultipleEntryDrawer;

export const useDealershipPriceDisclaimerMultipleEntryDrawer = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (
            props: Pick<
                DealershipPriceDisclaimerMultipleEntryDrawerProps,
                'name' | 'title' | 'renderField' | 'dealers' | 'disclaimerKind' | 'isEdit'
            >
        ) => (
            <DealershipPriceDisclaimerMultipleEntryDrawer
                {...props}
                name={props.name}
                onClose={actions.close}
                visible={visible}
            />
        ),
    };
};
