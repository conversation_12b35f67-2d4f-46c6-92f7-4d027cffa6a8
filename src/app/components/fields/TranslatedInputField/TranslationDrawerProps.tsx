import type { DrawerProps } from 'antd';
import type { LanguagePackOptionDataFragment } from '../../../api/fragments/LanguagePackOptionData';

export type TranslationDrawerProps = Pick<DrawerProps, 'title' | 'visible' | 'onClose'> & {
    name: string;
    languages?: LanguagePackOptionDataFragment[];
    renderField?: (item: TranslationItem, onDelete: () => void, disabled?: boolean) => JSX.Element;
    maxInputLength?: number;
    disabled?: boolean;
};

export type TranslationItem = {
    languagePackId: string;
    index: number;
    language: LanguagePackOptionDataFragment;
    name: string;
    maxLength?: number;
};
