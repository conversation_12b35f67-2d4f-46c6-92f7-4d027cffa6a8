import type { PickerProps } from 'antd/es/date-picker/generatePicker';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useField } from 'formik';
import { isNil } from 'lodash/fp';
import { customAlphabet } from 'nanoid';
import { forwardRef, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import DayjsDatePicker from './DayjsDatePicker';
import FormItem from './FormItem';

export const classNameGenerator = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz', 16);

export type TimeProps = PickerProps<Dayjs>;

export interface TimePickerProps extends TimeProps {}

const TimePicker = forwardRef<any, TimePickerProps>((props, ref) => (
    <DayjsDatePicker {...props} ref={ref} mode={undefined} picker="time" />
));

TimePicker.displayName = 'TimePicker';

const SharedTimePickerStyle = ($readOnly: boolean) => css`
    width: 100%;

    &.ant-picker {
        background-color: transparent;
        box-shadow: none;

        & input {
            font-size: var(--input-font-size, 16px);

            &::placeholder {
                font-size: var(--input-font-size, 16px);
                color: #535457;
            }
        }

        &.ant-picker-disabled {
            ${$readOnly &&
            css`
                background-color: #fff;

                & .ant-picker-input > input[disabled] {
                    color: #010205;
                    cursor: text;
                }
            `}

            background-color: transparent;

            & .ant-picker-suffix {
                display: none;
            }
        }

        & .anticon {
            color: var(--ant-primary-color);
        }

        & .ant-picker-content {
            max-height: 200px;
            height: auto;
        }

        & .ant-picker-time-panel-column::after {
            display: none;
        }
    }
`;

const StyledTimePicker = styled(TimePicker)<{ $readOnly: boolean }>`
    ${props => SharedTimePickerStyle(props.$readOnly)}

    &.ant-picker {
        border-top: none;
        border-left: none;
        border-right: none;
    }
`;

const AntdTimePicker = styled(TimePicker)<{ $readOnly: boolean }>`
    ${props => SharedTimePickerStyle(props.$readOnly)}
`;

export type TimePickerFieldProps = Omit<TimePickerProps, 'mode' | 'picker'> & {
    label?: string;
    required?: boolean;
    readOnly?: boolean;
    format?: string;
    errorIcon?: React.ReactNode;
    offset?: string;
    formTouched?: boolean;
    applyDefaultStyles?: boolean;
};

type TimePickerOnChangeHandler = (time: Dayjs | undefined) => unknown;

const TimePickerField = ({
    name,
    label,
    disabledTime,
    required,
    showNow,
    readOnly,
    format,
    onChange,
    errorIcon,
    offset,
    formTouched = false,
    applyDefaultStyles = true,
    ...props
}: TimePickerFieldProps) => {
    const { t } = useTranslation('common');
    const [field, meta, { setValue: setFieldValue, setTouched }] = useField({ name });
    const [value, setValue] = useState<Dayjs | undefined>(meta.initialValue ? dayjs(meta.initialValue) : undefined);

    useEffect(() => {
        if (formTouched) {
            setTouched(true);
        }
    }, [formTouched]);

    useEffect(() => {
        if (field.value) {
            // sync internal values
            setValue(dayjs(field.value));

            return;
        }

        setValue(undefined);
    }, [field.value]);

    const onEnhancedChange = useCallback<TimePickerOnChangeHandler>(
        period => {
            setTouched(true);
            if (period) {
                // update formik values
                setFieldValue(period);

                if (onChange) {
                    onChange(period, period.format('YYYY-MM-DD'));
                }

                return;
            }

            setFieldValue(undefined);
            if (onChange) {
                onChange(period, period.format('YYYY-MM-DD'));
            }
        },
        [onChange, setFieldValue, setTouched]
    );

    const className = useMemo(() => classNameGenerator(), []);

    const timeZoneAddedLabel = !isNil(offset) ? `${label} (UTC${offset})` : label;

    const EnhancedTimePicker = useMemo(
        () => (applyDefaultStyles ? StyledTimePicker : AntdTimePicker),
        [applyDefaultStyles]
    );

    return (
        <FormItem errorIcon={errorIcon} label={timeZoneAddedLabel} meta={meta} required={required}>
            <EnhancedTimePicker
                {...props}
                $readOnly={readOnly}
                className={className}
                disabledTime={disabledTime}
                format={format ?? t('common:formats.timePicker')}
                getPopupContainer={() => (className ? document.querySelector(`.${className}`) : null)}
                mode={undefined}
                name={name}
                onChange={onEnhancedChange}
                picker="time"
                showNow={showNow}
                value={value}
                inputReadOnly
            />
        </FormItem>
    );
};

export default TimePickerField;
