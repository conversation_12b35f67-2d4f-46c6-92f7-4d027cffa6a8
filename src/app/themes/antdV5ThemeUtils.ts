import type { ThemeConfig } from 'antd';

/**
 * Theme-specific configurations for each brand
 */
import { CompanyTheme } from '../api/types';

/**
 * Utility functions for converting theme tokens to Ant Design v5 format
 */

export interface ThemeTokens {
    primaryColor: string;
    hoveredPrimaryColor: string;
    disabledColor?: string;
    fontFamily?: string;
    buttonFontSize: string;
    inputFontSize: string;
    inputFieldHeight?: string;
    baseFontSize?: string;
}

/**
 * Creates an Ant Design v5 theme configuration from theme tokens
 */
export const createAntdV5Theme = (tokens: ThemeTokens): ThemeConfig => {
    const parseFontSize = (fontSize: string): number => {
        // Handle rem units
        if (fontSize.includes('rem')) {
            return Math.round(parseFloat(fontSize) * 16); // Convert rem to px (assuming 16px base)
        }

        // Handle px units
        return parseInt(fontSize, 10) || 14;
    };

    const parseHeight = (height: string): number => parseInt(height, 10) || 32;

    return {
        token: {
            // Primary colors
            colorPrimary: tokens.primaryColor,
            colorPrimaryHover: tokens.hoveredPrimaryColor,
            colorPrimaryActive: tokens.primaryColor,

            // Typography
            fontFamily:
                tokens.fontFamily ||
                "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif",
            fontSize: parseFontSize(tokens.inputFontSize),

            // Component specific tokens
            controlHeight: tokens.inputFieldHeight ? parseHeight(tokens.inputFieldHeight) : 32,
        },
        components: {
            Button: {
                fontSize: parseFontSize(tokens.buttonFontSize),
                controlHeight: tokens.inputFieldHeight ? parseHeight(tokens.inputFieldHeight) : 32,
            },
            Input: {
                fontSize: parseFontSize(tokens.inputFontSize),
                controlHeight: tokens.inputFieldHeight ? parseHeight(tokens.inputFieldHeight) : 32,
            },
            Select: {
                fontSize: parseFontSize(tokens.inputFontSize),
                controlHeight: tokens.inputFieldHeight ? parseHeight(tokens.inputFieldHeight) : 32,
            },
            Segmented: {
                itemSelectedBg: tokens.primaryColor,
                itemSelectedColor: '#fff',
                itemHoverColor: tokens.primaryColor,
                trackBg: 'rgba(0, 0, 0, 0.04)',
            },
            Tooltip: {
                colorBgSpotlight: 'rgba(0, 0, 0, 0.8)',
            },
            Popover: {
                // Custom styling for popover message title padding is handled in global.less
            },
        },
        algorithm: undefined, // Use default algorithm
    };
};

/**
 * Default theme tokens based on current system
 */
export const defaultThemeTokens: ThemeTokens = {
    primaryColor: '#1890ff',
    hoveredPrimaryColor: '#40a9ff',
    disabledColor: '#d9d9d9',
    fontFamily:
        "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",
    buttonFontSize: '14px',
    inputFontSize: '14px',
    inputFieldHeight: '32px',
};

/**
 * Merges theme tokens with defaults
 */
export const mergeThemeTokens = (customTokens: Partial<ThemeTokens>): ThemeTokens => ({
    ...defaultThemeTokens,
    ...customTokens,
});

export const getThemeTokensForBrand = (theme: CompanyTheme, customPrimaryColor?: string): ThemeTokens => {
    const baseTokens: Record<CompanyTheme, Partial<ThemeTokens>> = {
        [CompanyTheme.Default]: {
            primaryColor: '#010205',
            hoveredPrimaryColor: 'rgba(1, 2, 5, 0.67)',
            disabledColor: '#949598',
            fontFamily: "'Porsche Next','Arial Narrow',Arial,'Heiti SC',SimHei,sans-serif",
            buttonFontSize: '1rem',
            inputFontSize: '1rem',
            inputFieldHeight: '34px',
        },
        [CompanyTheme.Porsche]: {
            primaryColor: '#d5001c',
            hoveredPrimaryColor: '#980014',
            disabledColor: '#96989a',
            fontFamily: "'Porsche Next','Arial Narrow',Arial,'Heiti SC',SimHei,sans-serif",
            buttonFontSize: '1rem',
            inputFontSize: '1rem',
            inputFieldHeight: '48px',
        },
        [CompanyTheme.PorscheV3]: {
            primaryColor: '#d5001c', // Will be overridden by Porsche Design System tokens
            hoveredPrimaryColor: '#980014',
            fontFamily: "'Porsche Next','Arial Narrow',Arial,'Heiti SC',SimHei,sans-serif",
            buttonFontSize: '1rem',
            inputFontSize: '1rem',
            inputFieldHeight: '48px',
        },
        [CompanyTheme.Volkswagen]: {
            primaryColor: '#001E50',
            hoveredPrimaryColor: '#0f2e5c',
            buttonFontSize: '0.875rem',
            inputFontSize: '1rem',
        },
        [CompanyTheme.Skoda]: {
            primaryColor: '#0E3A2F',
            hoveredPrimaryColor: '#69b54e',
            buttonFontSize: '0.75rem',
            inputFontSize: '1rem',
        },
        [CompanyTheme.Admin]: {
            primaryColor: '#010205',
            hoveredPrimaryColor: 'rgba(1, 2, 5, 0.67)',
            disabledColor: '#949598',
            fontFamily: "'Porsche Next','Arial Narrow',Arial,'Heiti SC',SimHei,sans-serif",
            buttonFontSize: '0.875rem',
            inputFontSize: '0.875rem',
            inputFieldHeight: '32px',
        },
    };

    const tokens = mergeThemeTokens(baseTokens[theme] || {});

    // Override with custom primary color if provided (for company-specific theming)
    if (customPrimaryColor) {
        tokens.primaryColor = customPrimaryColor;
    }

    return tokens;
};
