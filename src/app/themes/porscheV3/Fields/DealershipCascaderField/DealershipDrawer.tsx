import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Space, Spin, Form, Result } from 'antd';
import { useField } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetDealersOptionsQuery } from '../../../../api/queries/getDealersOptions';
import type { ListDealerOption } from '../../../../api/types';
import DrawerWithAutoWidth from '../../../../components/DrawerWithAutoWidth';
import type { DrawerWithAutoWidthProps } from '../../../../components/DrawerWithAutoWidthProps';
import { useAddDealershipModal } from '../../../../components/fields/DealershipFields/AddDealershipModal';
import type { CascaderFieldProps } from '../../../../components/fields/shared';
import CustomDealershipCascaderField from './CustomDealershipCascaderField';
import Down from '../../../../icons/down.svg';

export type DealershipDrawerProps = Pick<DrawerWithAutoWidthProps, 'title' | 'visible' | 'onClose'> &
    Pick<CascaderFieldProps<{ children?: any; value: any }>, 'options'> & {
        name: string;
        dealers?: ListDealerOption[];
        renderField?: (
            item: DealershipItem,
            onDelete: () => void,
            options: CascaderFieldProps<{ children?: any; value: any }>['options']
        ) => JSX.Element;
    };

const DealershipDrawer = ({
    onClose,
    title,
    visible,
    name,
    dealers: dealersFromProps,
    renderField = renderCascaderField,
    options,
}: DealershipDrawerProps) => {
    const { t } = useTranslation(['dealershipField']);
    const [{ value: dealerships }, , { setValue }] = useField<{ dealerId: string; relatedId: string }[]>({
        name,
    });

    // to do - get dealers data
    const { data, loading } = useGetDealersOptionsQuery({
        fetchPolicy: 'cache-and-network',
        skip: !!dealersFromProps,
    });

    const dealersFromQuery = data?.dealers;

    const allDealers = useMemo(() => dealersFromProps || dealersFromQuery || [], [dealersFromProps, dealersFromQuery]);

    const remainingDealers = useMemo(() => {
        if (!dealerships) {
            return allDealers;
        }

        // exclude languages already translated
        return allDealers.filter(dealer => !dealerships.some(dealership => dealership.dealerId === dealer.id));
    }, [allDealers, dealerships]);

    const actions = useMemo(() => {
        const currentDealers = dealerships || [];

        return {
            add: (dealerId: string) => setValue([...currentDealers, { dealerId, relatedId: null }]),
            delete: (index: number) => {
                const newValue = [...currentDealers];
                newValue.splice(index, 1);
                setValue(newValue);
            },
        };
    }, [setValue, dealerships]);

    const addModal = useAddDealershipModal(remainingDealers, actions.add);

    const cleanedDealerships = useMemo((): DealershipItem[] => {
        if (!dealerships) {
            return [];
        }

        return dealerships
            .map((dealership, index) => {
                const dealer = allDealers.find(dealer => dealer.id === dealership.dealerId);

                return { ...dealership, dealer, name: `${name}[${index}].relatedId`, index, options };
            })
            .filter(dealership => !!dealership.dealer)
            .sort((a, b) => a.dealer.displayName.localeCompare(b.dealer.displayName));
    }, [dealerships, allDealers, name, options]);

    const content = (
        <Space direction="vertical" style={{ width: '100%' }}>
            {cleanedDealerships.length > 0 && (
                <Form layout="vertical">
                    {cleanedDealerships.map(dealership =>
                        renderField(dealership, () => actions.delete(dealership.index), options)
                    )}
                </Form>
            )}
            {remainingDealers.length > 0 && (
                <Button icon={<PlusOutlined />} onClick={addModal.open} type="dashed" block>
                    {t('dealershipField:default.addDealer')}
                </Button>
            )}
            {addModal.render()}
            {!cleanedDealerships.length && !remainingDealers.length && (
                <Result status="info" subTitle={t('dealershipField:noDealerRegistered')} />
            )}
        </Space>
    );

    return (
        <DrawerWithAutoWidth onClose={onClose} placement="right" title={title} visible={visible} destroyOnClose>
            <Spin spinning={loading}>{content}</Spin>
        </DrawerWithAutoWidth>
    );
};

export type DealershipItem = Pick<CascaderFieldProps<{ children?: any; value: any }>, 'options'> & {
    dealerId: string;
    index: number;
    dealer: ListDealerOption;
    name: string;
};

export const renderCascaderField: DealershipDrawerProps['renderField'] = (dealership, onDelete, options) => (
    <CustomDealershipCascaderField
        key={dealership.dealerId}
        label={dealership.dealer.displayName}
        name={dealership.name}
        options={options}
        suffixIcon={
            <>
                <Button icon={<DeleteOutlined />} onClick={onDelete} size="small" type="link" />
                <Down fill="var(--ant-primary-color)" />
            </>
        }
    />
);

export default DealershipDrawer;

export const useDealershipDrawer = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (props: Pick<DealershipDrawerProps, 'name' | 'title' | 'renderField' | 'dealers' | 'options'>) => (
            <DealershipDrawer {...props} name={`${props.name}.overrides`} onClose={actions.close} visible={visible} />
        ),
    };
};
