import { Layout as AntLayout, Typography, <PERSON>u as AntMenu } from 'antd';
import styled from 'styled-components';

export const Content = styled(AntLayout.Content)`
    margin-top: calc(var(--layout-header-height, 64px) + 10px);
    padding: var(--layout-body-padding, 10px 20px);
    width: 100%;
    padding-left: 35px;
`;

export const FooterText = styled(Typography.Paragraph)`
    text-align: center;
`;

export const Header = styled(AntLayout.Header)`
    position: fixed;
    width: 100%;
    top: 0;
    right: 0;
    padding: 0;
    z-index: 1;
`;

export const HeaderContainer = styled.div`
    display: flex;
    flex: 0 0 auto;
    align-items: center;
    padding: var(--layout-header-padding, 0 50px);
`;

export const Layout = styled(AntLayout)`
    max-width: var(--layout-max-width, 1280px);
    margin: 0 auto;
`;

export const LogoContainer = styled.div`
    position: absolute;
    right: 0;
    left: 0;
    margin: auto;
    height: auto;
    width: 200px;
    text-align: center;
    cursor: pointer;

    a,
    a:hover,
    a:focus {
        color: var(--heading-color, rgba(0, 0, 0, 0.85));
        font-size: 1.25em;
        text-decoration: none;
        line-height: inherit;
        user-select: none;
    }

    img {
        @media (max-width: 425px) {
            max-height: 40px !important;
            max-width: 30px !important;
        }
        max-width: clamp(96px, 100vw - 16.125rem, 200px);
        max-height: 60px;
        object-fit: contain;
        top: -1.5px;
    }
`;

const EnhancedMenu = props => <AntMenu {...props} theme="light" />;

export const Menu = styled(EnhancedMenu)`
    margin-left: 16px;
    line-height: 60px !important;
    height: 62px;
    border-bottom: none;
    flex: auto;
`;

export const LeftMenuContainer = styled(EnhancedMenu)`
    position: fixed;
    height: 100%;
    left: 0;
    margin-top: 64px;
    z-index: 100;
`;

export const CollapsedButtonContainer = styled.div`
    width: 100%;
    text-align: right;
`;

export const AvatarContainer = styled.div`
    width: 100%;
    padding: 30px;
`;

export const AvatarPanel = styled.div`
    border-bottom: 1px solid gray;
    text-align: center;
    padding: 30px;
`;

export const LeftMenu = styled(EnhancedMenu)`
    line-height: 60px !important;
    width: 256px;
`;
