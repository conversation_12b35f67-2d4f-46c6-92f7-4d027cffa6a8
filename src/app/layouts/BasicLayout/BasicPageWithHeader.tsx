import { ProPageHeader } from '@ant-design/pro-layout';
import React from 'react';

const BasicPageWithHeader: React.FC<{ children: React.ReactNode; extra: React.ReactNode; title: String }> = ({
    children,
    extra,
    title,
    ...props
}) => (
    <ProPageHeader extra={extra} ghost={false} prefixedClassName={null} title={title} {...props}>
        {children}
    </ProPageHeader>
);

export default BasicPageWithHeader;
